import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, MessageCircle, AlertTriangle } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { formatDistanceToNow } from "date-fns";

export function RecentActivity() {
  const { data: activities, isLoading } = useQuery({
    queryKey: ["/api/activity"],
    queryFn: api.getActivity,
  });

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "review":
        return <Star className="w-4 h-4 text-green-600" />;
      case "reply":
        return <MessageCircle className="w-4 h-4 text-blue-600" />;
      case "attention":
        return <AlertTriangle className="w-4 h-4 text-amber-600" />;
      default:
        return <Star className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActivityBgColor = (type: string) => {
    switch (type) {
      case "review":
        return "bg-green-100 dark:bg-green-900";
      case "reply":
        return "bg-blue-100 dark:bg-blue-900";
      case "attention":
        return "bg-amber-100 dark:bg-amber-900";
      default:
        return "bg-gray-100 dark:bg-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Activity</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Latest reviews and system updates
                </p>
              </div>
              <div className="skeleton h-8 w-16 rounded" />
            </div>
          </CardHeader>
          <CardContent className="divide-y divide-border">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="py-6">
                <div className="flex space-x-3">
                  <div className="skeleton w-8 h-8 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="skeleton h-4 w-48" />
                      <div className="skeleton h-3 w-20" />
                    </div>
                    <div className="skeleton h-4 w-full" />
                    <div className="flex items-center space-x-2">
                      <div className="skeleton h-5 w-24 rounded-full" />
                      <div className="skeleton h-3 w-16" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="lg:col-span-2">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Activity</CardTitle>
              <p className="text-sm text-muted-foreground">
                Latest reviews and system updates
              </p>
            </div>
            <Button variant="ghost" size="sm">
              View all
            </Button>
          </div>
        </CardHeader>
        <CardContent className="divide-y divide-border">
          {activities && activities.length > 0 ? (
            activities.map((activity: any) => (
              <div
                key={activity.id}
                className="py-6 hover:bg-accent/50 transition-colors -mx-6 px-6"
              >
                <div className="flex space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getActivityBgColor(activity.type)}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-foreground">
                        {activity.title}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                      </p>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant="secondary" className={`platform-badge ${activity.platform}`}>
                        {activity.platform === "google" ? "Google Business" : activity.platform}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        by {activity.guestName}
                      </span>
                      {activity.needsAttention && (
                        <Button variant="link" size="sm" className="text-xs p-0 h-auto">
                          Reply now
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="py-8 text-center">
              <MessageCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No recent activity</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
