import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/lib/api";
import type { PlatformConnection } from "@shared/schema";

export function PlatformStatus() {
  const queryClient = useQueryClient();

  const { data: connections, isLoading } = useQuery<PlatformConnection[]>({
    queryKey: ["/api/platforms/connected"],
    queryFn: api.getPlatformConnections,
  });

  const syncMutation = useMutation({
    mutationFn: api.syncGoogleReviews,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/platforms/connected"] });
      queryClient.invalidateQueries({ queryKey: ["/api/reviews"] });
      queryClient.invalidateQueries({ queryKey: ["/api/statistics"] });
    },
  });

  const connectGoogleMutation = useMutation({
    mutationFn: api.getGoogleAuthUrl,
    onSuccess: (data) => {
      window.location.href = data.authUrl;
    },
  });

  const handleConnect = async (platform: string) => {
    if (platform === "google") {
      await connectGoogleMutation.mutateAsync();
    } else {
      // Handle other platforms
      console.log(`Connecting to ${platform}...`);
    }
  };

  const handleSync = async (platform: string) => {
    if (platform === "google") {
      await syncMutation.mutateAsync();
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "google":
        return (
          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-red-500 rounded" />
        );
      case "tripadvisor":
        return (
          <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">T</span>
          </div>
        );
      case "booking":
        return (
          <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">B</span>
          </div>
        );
      default:
        return null;
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case "google":
        return "Google Business";
      case "tripadvisor":
        return "TripAdvisor";
      case "booking":
        return "Booking.com";
      default:
        return platform;
    }
  };

  const getStatusBadge = (connection: PlatformConnection) => {
    if (!connection.connected) {
      return <Badge variant="secondary">Not connected</Badge>;
    }

    switch (connection.status) {
      case "active":
        return (
          <div className="flex items-center space-x-2">
            <div className="status-indicator active" />
            <span className="text-sm text-green-700 dark:text-green-300">Active</span>
          </div>
        );
      case "error":
        return (
          <div className="flex items-center space-x-2">
            <div className="status-indicator error" />
            <span className="text-sm text-red-700 dark:text-red-300">Error</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2">
            <div className="status-indicator inactive" />
            <span className="text-sm text-gray-700 dark:text-gray-300">Inactive</span>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle>Platform Connections</CardTitle>
            <p className="text-sm text-muted-foreground">
              Manage your review platform integrations
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="skeleton w-10 h-10 rounded-lg" />
                  <div className="space-y-1">
                    <div className="skeleton h-4 w-24" />
                    <div className="skeleton h-3 w-32" />
                  </div>
                </div>
                <div className="skeleton h-8 w-20 rounded-md" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="lg:col-span-1">
      <Card>
        <CardHeader>
          <CardTitle>Platform Connections</CardTitle>
          <p className="text-sm text-muted-foreground">
            Manage your review platform integrations
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {connections?.map((connection) => (
            <div
              key={connection.platform}
              className={`flex items-center justify-between p-4 rounded-lg border transition-colors ${
                connection.connected
                  ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                  : "bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-lg flex items-center justify-center shadow-sm">
                  {getPlatformIcon(connection.platform)}
                </div>
                <div>
                  <p className="font-medium text-foreground">
                    {getPlatformName(connection.platform)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {connection.connected
                      ? `Connected • ${connection.reviewCount} reviews`
                      : "Not connected"}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {connection.connected ? (
                  <>
                    {getStatusBadge(connection)}
                    {connection.platform === "google" && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSync(connection.platform)}
                        disabled={syncMutation.isPending}
                      >
                        {syncMutation.isPending ? "Syncing..." : "Sync"}
                      </Button>
                    )}
                  </>
                ) : (
                  <Button
                    size="sm"
                    onClick={() => handleConnect(connection.platform)}
                    disabled={connectGoogleMutation.isPending}
                  >
                    {connectGoogleMutation.isPending ? "Connecting..." : "Connect"}
                  </Button>
                )}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
