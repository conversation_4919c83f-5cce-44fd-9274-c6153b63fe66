import { 
  users, hotels, reviews, replies, platformTokens, googleBusinessLocations, syncStatus,
  type User, type InsertUser, type Hotel, type InsertHotel, 
  type Review, type InsertReview, type Reply, type InsertReply,
  type PlatformToken, type InsertPlatformToken, type GoogleBusinessLocation,
  type InsertGoogleBusinessLocation, type SyncStatus, type ReviewStats, type PlatformConnection
} from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;

  // Hotel management
  getHotel(id: number): Promise<Hotel | undefined>;
  createHotel(hotel: InsertHotel): Promise<Hotel>;
  updateHotel(id: number, updates: Partial<Hotel>): Promise<Hotel | undefined>;

  // Review management
  getReview(id: number): Promise<Review | undefined>;
  getReviews(filters?: { hotelId?: number; platform?: string; rating?: number; hasReply?: boolean; needsAttention?: boolean }): Promise<Review[]>;
  createReview(review: InsertReview): Promise<Review>;
  updateReview(id: number, updates: Partial<Review>): Promise<Review | undefined>;
  getReviewByPlatformId(platform: string, platformReviewId: string): Promise<Review | undefined>;

  // Reply management
  getReply(id: number): Promise<Reply | undefined>;
  getRepliesByReviewId(reviewId: number): Promise<Reply[]>;
  createReply(reply: InsertReply): Promise<Reply>;
  updateReply(id: number, updates: Partial<Reply>): Promise<Reply | undefined>;

  // Platform token management
  getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined>;
  createPlatformToken(token: InsertPlatformToken): Promise<PlatformToken>;
  updatePlatformToken(id: number, updates: Partial<PlatformToken>): Promise<PlatformToken | undefined>;

  // Google Business locations
  getGoogleBusinessLocations(hotelId: number): Promise<GoogleBusinessLocation[]>;
  createGoogleBusinessLocation(location: InsertGoogleBusinessLocation): Promise<GoogleBusinessLocation>;
  updateGoogleBusinessLocation(id: number, updates: Partial<GoogleBusinessLocation>): Promise<GoogleBusinessLocation | undefined>;

  // Sync status
  getSyncStatus(hotelId: number, platform: string): Promise<SyncStatus | undefined>;
  updateSyncStatus(hotelId: number, platform: string, status: Partial<SyncStatus>): Promise<SyncStatus>;

  // Statistics
  getReviewStats(hotelId: number): Promise<ReviewStats>;
  getPlatformConnections(hotelId: number): Promise<PlatformConnection[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User> = new Map();
  private hotels: Map<number, Hotel> = new Map();
  private reviews: Map<number, Review> = new Map();
  private replies: Map<number, Reply> = new Map();
  private platformTokens: Map<number, PlatformToken> = new Map();
  private googleBusinessLocations: Map<number, GoogleBusinessLocation> = new Map();
  private syncStatuses: Map<string, SyncStatus> = new Map();
  
  private currentUserId = 1;
  private currentHotelId = 1;
  private currentReviewId = 1;
  private currentReplyId = 1;
  private currentTokenId = 1;
  private currentLocationId = 1;
  private currentSyncId = 1;

  constructor() {
    this.seedData();
  }

  private seedData() {
    // Seed hotel
    const hotel: Hotel = {
      id: this.currentHotelId++,
      name: "Grand Palace Hotel",
      address: "123 Luxury Avenue",
      city: "New York",
      country: "USA",
      phoneNumber: "******-0123",
      email: "<EMAIL>",
      website: "https://grandpalacehotel.com",
      description: "A luxury hotel in the heart of the city",
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.hotels.set(hotel.id, hotel);

    // Seed admin user
    const user: User = {
      id: this.currentUserId++,
      username: "admin",
      email: "<EMAIL>",
      password: "$2b$10$example", // In real app, hash the password
      firstName: "Sarah",
      lastName: "Johnson",
      role: "manager",
      hotelId: hotel.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(user.id, user);

    // Seed some realistic reviews with varied timestamps
    const reviewsData = [
      {
        guestName: "Michael Chen",
        rating: 5,
        content: "Exceptional service and beautiful rooms. The staff went above and beyond to make our anniversary celebration special. The concierge team helped us arrange a perfect dinner reservation and the room was decorated with rose petals. Will definitely return!",
        roomNumber: "1205",
        stayDuration: "3-night stay",
        reviewDate: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
        hasReply: false,
        needsAttention: false,
        priority: "normal",
      },
      {
        guestName: "Emma Rodriguez",
        rating: 4,
        content: "Great location and friendly staff. The room was clean and comfortable. Only issue was the room service took longer than expected - about 45 minutes for breakfast. Otherwise, had a wonderful stay!",
        roomNumber: "823",
        stayDuration: "2-night stay",
        reviewDate: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
        hasReply: true,
        needsAttention: false,
        priority: "normal",
      },
      {
        guestName: "David Park",
        rating: 3,
        content: "The hotel has potential but needs improvement. Room 312 had a broken air conditioning unit and maintenance took 3 hours to fix it. The front desk staff was apologetic but this shouldn't happen in a hotel of this caliber.",
        roomNumber: "312",
        stayDuration: "4-night stay",
        reviewDate: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        hasReply: false,
        needsAttention: true,
        priority: "high",
      },
      {
        guestName: "Jennifer Smith",
        rating: 5,
        content: "Outstanding experience from check-in to check-out. The spa services were incredible and the breakfast buffet had amazing variety. Room was spotless and the view was breathtaking.",
        roomNumber: "1001",
        stayDuration: "5-night stay",
        reviewDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        hasReply: true,
        needsAttention: false,
        priority: "normal",
      },
      {
        guestName: "Robert Wilson",
        rating: 2,
        content: "Disappointed with our stay. The WiFi was constantly dropping, the air conditioning was too loud, and housekeeping missed our room one day. For the price we paid, we expected much better service.",
        roomNumber: "507",
        stayDuration: "3-night stay",
        reviewDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        hasReply: false,
        needsAttention: true,
        priority: "urgent",
      },
      {
        guestName: "Lisa Thompson",
        rating: 4,
        content: "Good overall experience. The location is perfect for exploring the city. Staff was helpful and rooms were comfortable. The only downside was the noise from the street early in the morning.",
        roomNumber: "203",
        stayDuration: "2-night stay",
        reviewDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        hasReply: true,
        needsAttention: false,
        priority: "normal",
      },
    ];

    reviewsData.forEach((reviewData) => {
      const review: Review = {
        id: this.currentReviewId++,
        hotelId: hotel.id,
        platform: "google",
        platformReviewId: `google_${Date.now()}_${Math.random()}`,
        guestName: reviewData.guestName,
        guestEmail: null,
        rating: reviewData.rating,
        title: null,
        content: reviewData.content,
        reviewDate: reviewData.reviewDate,
        roomNumber: reviewData.roomNumber,
        stayDuration: reviewData.stayDuration,
        isVerifiedStay: true,
        metadata: null,
        hasReply: reviewData.hasReply,
        needsAttention: reviewData.needsAttention,
        priority: reviewData.priority,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      this.reviews.set(review.id, review);

      // Add replies for reviews that have them
      if (reviewData.hasReply) {
        const replyContent = this.generateReplyContent(reviewData.rating, reviewData.content);
        const reply: Reply = {
          id: this.currentReplyId++,
          reviewId: review.id,
          userId: user.id,
          content: replyContent,
          isPublished: true,
          publishedAt: new Date(reviewData.reviewDate.getTime() + 2 * 60 * 60 * 1000), // 2 hours after review
          platformReplyId: `reply_${Date.now()}_${Math.random()}`,
          followUpAction: reviewData.rating <= 3 ? "contact_guest" : "none",
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        this.replies.set(reply.id, reply);
      }
    });

    // Seed Google Business connection
    const googleToken: PlatformToken = {
      id: this.currentTokenId++,
      hotelId: hotel.id,
      platform: "google",
      accessToken: "mock_access_token",
      refreshToken: "mock_refresh_token",
      tokenType: "Bearer",
      expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
      scope: "https://www.googleapis.com/auth/business.manage",
      isActive: true,
      lastSyncAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.platformTokens.set(googleToken.id, googleToken);

    // Seed Google Business location
    const googleLocation: GoogleBusinessLocation = {
      id: this.currentLocationId++,
      hotelId: hotel.id,
      locationId: "ChIJN1t_tDeuEmsRUsoyG83frY4",
      accountId: "accounts/12345",
      name: "Grand Palace Hotel",
      address: "123 Luxury Avenue, New York, NY 10001",
      phoneNumber: "******-0123",
      website: "https://grandpalacehotel.com",
      averageRating: "4.2",
      totalReviewCount: this.reviews.size,
      isVerified: true,
      status: "active",
      lastSyncAt: new Date(Date.now() - 5 * 60 * 1000),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.googleBusinessLocations.set(googleLocation.id, googleLocation);
  }

  private generateReplyContent(rating: number, reviewContent: string): string {
    if (rating >= 4) {
      return "Thank you so much for your wonderful review! We're thrilled to hear about your positive experience. We look forward to welcoming you back soon!";
    } else if (rating === 3) {
      return "Thank you for your feedback. We appreciate you taking the time to share your experience. We're always working to improve and would love to make your next stay even better.";
    } else {
      return "Thank you for bringing these issues to our attention. We sincerely apologize for the inconvenience during your stay. We take all feedback seriously and are working to address these concerns immediately. We would welcome the opportunity to make this right.";
    }
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const user: User = {
      ...insertUser,
      id: this.currentUserId++,
      role: insertUser.role || "staff",
      hotelId: insertUser.hotelId || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(user.id, user);
    return user;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    
    const updatedUser = { ...user, ...updates, updatedAt: new Date() };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  // Hotel methods
  async getHotel(id: number): Promise<Hotel | undefined> {
    return this.hotels.get(id);
  }

  async createHotel(insertHotel: InsertHotel): Promise<Hotel> {
    const hotel: Hotel = {
      ...insertHotel,
      id: this.currentHotelId++,
      address: insertHotel.address || null,
      city: insertHotel.city || null,
      country: insertHotel.country || null,
      phoneNumber: insertHotel.phoneNumber || null,
      email: insertHotel.email || null,
      website: insertHotel.website || null,
      description: insertHotel.description || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.hotels.set(hotel.id, hotel);
    return hotel;
  }

  async updateHotel(id: number, updates: Partial<Hotel>): Promise<Hotel | undefined> {
    const hotel = this.hotels.get(id);
    if (!hotel) return undefined;
    
    const updatedHotel = { ...hotel, ...updates, updatedAt: new Date() };
    this.hotels.set(id, updatedHotel);
    return updatedHotel;
  }

  // Review methods
  async getReview(id: number): Promise<Review | undefined> {
    return this.reviews.get(id);
  }

  async getReviews(filters?: { hotelId?: number; platform?: string; rating?: number; hasReply?: boolean; needsAttention?: boolean }): Promise<Review[]> {
    let reviews = Array.from(this.reviews.values());
    
    if (filters) {
      if (filters.hotelId !== undefined) {
        reviews = reviews.filter(r => r.hotelId === filters.hotelId);
      }
      if (filters.platform) {
        reviews = reviews.filter(r => r.platform === filters.platform);
      }
      if (filters.rating !== undefined) {
        reviews = reviews.filter(r => r.rating === filters.rating);
      }
      if (filters.hasReply !== undefined) {
        reviews = reviews.filter(r => r.hasReply === filters.hasReply);
      }
      if (filters.needsAttention !== undefined) {
        reviews = reviews.filter(r => r.needsAttention === filters.needsAttention);
      }
    }
    
    return reviews.sort((a, b) => b.reviewDate.getTime() - a.reviewDate.getTime());
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const review: Review = {
      ...insertReview,
      id: this.currentReviewId++,
      title: insertReview.title || null,
      guestEmail: insertReview.guestEmail || null,
      roomNumber: insertReview.roomNumber || null,
      stayDuration: insertReview.stayDuration || null,
      isVerifiedStay: insertReview.isVerifiedStay || null,
      metadata: insertReview.metadata || null,
      hasReply: insertReview.hasReply || null,
      needsAttention: insertReview.needsAttention || null,
      priority: insertReview.priority || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.reviews.set(review.id, review);
    return review;
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review | undefined> {
    const review = this.reviews.get(id);
    if (!review) return undefined;
    
    const updatedReview = { ...review, ...updates, updatedAt: new Date() };
    this.reviews.set(id, updatedReview);
    return updatedReview;
  }

  async getReviewByPlatformId(platform: string, platformReviewId: string): Promise<Review | undefined> {
    return Array.from(this.reviews.values()).find(
      r => r.platform === platform && r.platformReviewId === platformReviewId
    );
  }

  // Reply methods
  async getReply(id: number): Promise<Reply | undefined> {
    return this.replies.get(id);
  }

  async getRepliesByReviewId(reviewId: number): Promise<Reply[]> {
    return Array.from(this.replies.values()).filter(r => r.reviewId === reviewId);
  }

  async createReply(insertReply: InsertReply): Promise<Reply> {
    const reply: Reply = {
      ...insertReply,
      id: this.currentReplyId++,
      followUpAction: insertReply.followUpAction || null,
      isPublished: insertReply.isPublished || null,
      publishedAt: insertReply.publishedAt || null,
      platformReplyId: insertReply.platformReplyId || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.replies.set(reply.id, reply);

    // Update the review to mark it as replied
    const review = this.reviews.get(insertReply.reviewId);
    if (review) {
      this.reviews.set(review.id, {
        ...review,
        hasReply: true,
        needsAttention: false,
        updatedAt: new Date(),
      });
    }

    return reply;
  }

  async updateReply(id: number, updates: Partial<Reply>): Promise<Reply | undefined> {
    const reply = this.replies.get(id);
    if (!reply) return undefined;
    
    const updatedReply = { ...reply, ...updates, updatedAt: new Date() };
    this.replies.set(id, updatedReply);
    return updatedReply;
  }

  // Platform token methods
  async getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined> {
    return Array.from(this.platformTokens.values()).find(
      t => t.hotelId === hotelId && t.platform === platform && t.isActive
    );
  }

  async createPlatformToken(insertToken: InsertPlatformToken): Promise<PlatformToken> {
    const token: PlatformToken = {
      ...insertToken,
      id: this.currentTokenId++,
      accessToken: insertToken.accessToken || null,
      refreshToken: insertToken.refreshToken || null,
      tokenType: insertToken.tokenType || null,
      expiresAt: insertToken.expiresAt || null,
      scope: insertToken.scope || null,
      isActive: insertToken.isActive || null,
      lastSyncAt: insertToken.lastSyncAt || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.platformTokens.set(token.id, token);
    return token;
  }

  async updatePlatformToken(id: number, updates: Partial<PlatformToken>): Promise<PlatformToken | undefined> {
    const token = this.platformTokens.get(id);
    if (!token) return undefined;
    
    const updatedToken = { ...token, ...updates, updatedAt: new Date() };
    this.platformTokens.set(id, updatedToken);
    return updatedToken;
  }

  // Google Business location methods
  async getGoogleBusinessLocations(hotelId: number): Promise<GoogleBusinessLocation[]> {
    return Array.from(this.googleBusinessLocations.values()).filter(l => l.hotelId === hotelId);
  }

  async createGoogleBusinessLocation(insertLocation: InsertGoogleBusinessLocation): Promise<GoogleBusinessLocation> {
    const location: GoogleBusinessLocation = {
      ...insertLocation,
      id: this.currentLocationId++,
      address: insertLocation.address || null,
      phoneNumber: insertLocation.phoneNumber || null,
      website: insertLocation.website || null,
      averageRating: insertLocation.averageRating || null,
      totalReviewCount: insertLocation.totalReviewCount || null,
      isVerified: insertLocation.isVerified || null,
      status: insertLocation.status || null,
      lastSyncAt: insertLocation.lastSyncAt || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.googleBusinessLocations.set(location.id, location);
    return location;
  }

  async updateGoogleBusinessLocation(id: number, updates: Partial<GoogleBusinessLocation>): Promise<GoogleBusinessLocation | undefined> {
    const location = this.googleBusinessLocations.get(id);
    if (!location) return undefined;
    
    const updatedLocation = { ...location, ...updates, updatedAt: new Date() };
    this.googleBusinessLocations.set(id, updatedLocation);
    return updatedLocation;
  }

  // Sync status methods
  async getSyncStatus(hotelId: number, platform: string): Promise<SyncStatus | undefined> {
    return this.syncStatuses.get(`${hotelId}_${platform}`);
  }

  async updateSyncStatus(hotelId: number, platform: string, statusUpdates: Partial<SyncStatus>): Promise<SyncStatus> {
    const key = `${hotelId}_${platform}`;
    const existing = this.syncStatuses.get(key);
    
    const status: SyncStatus = {
      id: existing?.id || this.currentSyncId++,
      hotelId,
      platform,
      lastSyncAt: null,
      nextSyncAt: null,
      status: "pending",
      recordsProcessed: 0,
      recordsAdded: 0,
      recordsUpdated: 0,
      errorMessage: null,
      createdAt: existing?.createdAt || new Date(),
      updatedAt: new Date(),
      ...existing,
      ...statusUpdates,
    };
    
    this.syncStatuses.set(key, status);
    return status;
  }

  // Statistics methods
  async getReviewStats(hotelId: number): Promise<ReviewStats> {
    const reviews = await this.getReviews({ hotelId });
    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0 
      ? Number((reviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews).toFixed(1))
      : 0;
    
    const pendingReplies = reviews.filter(r => !r.hasReply).length;
    const responseRate = totalReviews > 0 
      ? Math.round((reviews.filter(r => r.hasReply).length / totalReviews) * 100)
      : 0;

    const ratingDistribution = reviews.reduce((acc, review) => {
      acc[review.rating.toString()] = (acc[review.rating.toString()] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Generate monthly trend (last 6 months)
    const monthlyTrend = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthReviews = reviews.filter(r => {
        const reviewMonth = r.reviewDate.getMonth();
        const reviewYear = r.reviewDate.getFullYear();
        return reviewMonth === date.getMonth() && reviewYear === date.getFullYear();
      });
      
      monthlyTrend.push({
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        count: monthReviews.length,
        rating: monthReviews.length > 0 
          ? Number((monthReviews.reduce((sum, r) => sum + r.rating, 0) / monthReviews.length).toFixed(1))
          : 0,
      });
    }

    return {
      totalReviews,
      averageRating,
      pendingReplies,
      responseRate,
      ratingDistribution,
      monthlyTrend,
    };
  }

  async getPlatformConnections(hotelId: number): Promise<PlatformConnection[]> {
    const googleToken = await this.getPlatformToken(hotelId, "google");
    const googleReviews = await this.getReviews({ hotelId, platform: "google" });
    
    return [
      {
        platform: "google",
        connected: !!googleToken,
        reviewCount: googleReviews.length,
        lastSync: googleToken?.lastSyncAt?.toISOString() || null,
        status: googleToken ? "active" : "inactive",
      },
      {
        platform: "tripadvisor",
        connected: false,
        reviewCount: 0,
        lastSync: null,
        status: "inactive",
      },
      {
        platform: "booking",
        connected: false,
        reviewCount: 0,
        lastSync: null,
        status: "inactive",
      },
    ];
  }
}

import { SQLiteStorage } from "./database";

export const storage = new SQLiteStorage();
