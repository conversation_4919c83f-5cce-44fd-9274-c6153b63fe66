import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Star, X } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { api } from "@/lib/api";
import { replySchema, type ReplyRequest } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";

interface ReplyModalProps {
  reviewId: number;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function ReplyModal({ reviewId, isOpen, onClose, onSuccess }: ReplyModalProps) {
  const [selectedTemplate, setSelectedTemplate] = useState("custom");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: review, isLoading } = useQuery({
    queryKey: ["/api/reviews", reviewId],
    queryFn: () => api.getReview(reviewId),
    enabled: isOpen && !!reviewId,
  });

  const form = useForm<ReplyRequest>({
    resolver: zodResolver(replySchema),
    defaultValues: {
      content: "",
      followUpAction: "none",
    },
  });

  const replyMutation = useMutation({
    mutationFn: (data: ReplyRequest) => api.replyToReview(reviewId, data),
    onSuccess: () => {
      toast({
        title: "Reply sent successfully",
        description: "Your response has been posted to the review platform.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/reviews"] });
      queryClient.invalidateQueries({ queryKey: ["/api/statistics"] });
      onSuccess();
      form.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Failed to send reply",
        description: error.message || "An error occurred while posting your reply.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    replyMutation.mutate(data);
  });

  const handleSaveDraft = () => {
    toast({
      title: "Draft saved",
      description: "Your reply has been saved as a draft.",
    });
  };

  const templates = {
    "custom": "",
    "maintenance": "Thank you for bringing this to our attention. We sincerely apologize for the inconvenience with the maintenance issue. We take all feedback seriously and are working to ensure this doesn't happen again. We would love the opportunity to make this right.",
    "service_recovery": "Thank you for your feedback. We apologize that your experience didn't meet your expectations. We take all guest concerns seriously and would appreciate the opportunity to discuss this further and make improvements.",
    "general_apology": "Thank you for taking the time to share your experience. We apologize for any inconvenience and appreciate your feedback as it helps us improve our services.",
  };

  const handleTemplateChange = (template: string) => {
    setSelectedTemplate(template);
    if (template && templates[template as keyof typeof templates]) {
      form.setValue("content", templates[template as keyof typeof templates]);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-3 h-3 ${
            i <= rating ? "text-yellow-400 fill-current" : "text-gray-300 dark:text-gray-600"
          }`}
        />
      );
    }
    return stars;
  };

  const contentLength = form.watch("content")?.length || 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Reply to Review
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4">
            <div className="skeleton h-20 w-full" />
            <div className="skeleton h-32 w-full" />
            <div className="skeleton h-10 w-full" />
          </div>
        ) : review ? (
          <div className="space-y-6">
            {/* Review Preview */}
            <div className="p-3 bg-muted rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <span className="font-medium text-foreground">{review.guestName}</span>
                <div className="flex space-x-1">
                  {renderStars(review.rating)}
                </div>
                <Badge className="platform-badge google">
                  {review.platform === "google" ? "Google Business" : review.platform}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {review.content.length > 200 
                  ? `${review.content.substring(0, 200)}...` 
                  : review.content
                }
              </p>
            </div>

            {/* Reply Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="content">Your Response</Label>
                <Textarea
                  id="content"
                  placeholder="Thank you for your feedback. We sincerely apologize for the inconvenience..."
                  className="min-h-[100px] resize-none"
                  {...form.register("content")}
                />
                <div className="flex justify-between items-center">
                  <p className="text-xs text-muted-foreground">
                    Character count: {contentLength}/1000
                  </p>
                  {form.formState.errors.content && (
                    <p className="text-xs text-destructive">
                      {form.formState.errors.content.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="template">Response Template</Label>
                  <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Custom Response" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="custom">Custom Response</SelectItem>
                      <SelectItem value="maintenance">Maintenance Issue</SelectItem>
                      <SelectItem value="service_recovery">Service Recovery</SelectItem>
                      <SelectItem value="general_apology">General Apology</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="followUpAction">Follow-up Action</Label>
                  <Select 
                    value={form.watch("followUpAction")} 
                    onValueChange={(value) => form.setValue("followUpAction", value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="contact_guest">Contact Guest Directly</SelectItem>
                      <SelectItem value="compensation">Send Compensation</SelectItem>
                      <SelectItem value="schedule_followup">Schedule Follow-up</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Footer Actions */}
              <div className="flex items-center justify-between pt-4 border-t">
                <Button 
                  type="button" 
                  variant="ghost" 
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <div className="flex items-center space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSaveDraft}
                  >
                    Save Draft
                  </Button>
                  <Button
                    type="submit"
                    disabled={replyMutation.isPending || !form.watch("content")?.trim()}
                  >
                    {replyMutation.isPending ? "Sending..." : "Send Reply"}
                  </Button>
                </div>
              </div>
            </form>
          </div>
        ) : (
          <div className="py-8 text-center">
            <p className="text-muted-foreground">Review not found</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
