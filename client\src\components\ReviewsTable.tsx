import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { ReviewCard } from "./ReviewCard";
import { ReplyModal } from "./ReplyModal";
import type { Review } from "@shared/schema";

export function ReviewsTable() {
  const [filters, setFilters] = useState({
    platform: "all",
    rating: "all",
    hasReply: "all",
    needsAttention: "all",
  });
  const [selectedReviewId, setSelectedReviewId] = useState<number | null>(null);
  const queryClient = useQueryClient();

  const buildQueryParams = () => {
    const params: any = {};
    if (filters.platform && filters.platform !== "all") params.platform = filters.platform;
    if (filters.rating && filters.rating !== "all") params.rating = parseInt(filters.rating);
    if (filters.hasReply && filters.hasReply !== "all") params.hasReply = filters.hasReply === "true";
    if (filters.needsAttention && filters.needsAttention !== "all") params.needsAttention = filters.needsAttention === "true";
    return params;
  };

  const { data: reviews, isLoading } = useQuery<Review[]>({
    queryKey: ["/api/reviews", filters],
    queryFn: () => api.getReviews(buildQueryParams()),
  });

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleReply = (reviewId: number) => {
    setSelectedReviewId(reviewId);
  };

  const handleReplySuccess = () => {
    setSelectedReviewId(null);
    queryClient.invalidateQueries({ queryKey: ["/api/reviews"] });
    queryClient.invalidateQueries({ queryKey: ["/api/statistics"] });
  };

  const handleExportReviews = () => {
    if (!reviews) return;

    const csvData = reviews.map(review => ({
      Date: new Date(review.reviewDate).toLocaleDateString(),
      Platform: review.platform,
      Guest: review.guestName,
      Rating: review.rating,
      Content: review.content.replace(/"/g, '""'), // Escape quotes
      Room: review.roomNumber || "",
      Duration: review.stayDuration || "",
      HasReply: review.hasReply ? "Yes" : "No",
      NeedsAttention: review.needsAttention ? "Yes" : "No",
    }));

    const csvHeaders = Object.keys(csvData[0]).join(",");
    const csvRows = csvData.map(row => 
      Object.values(row).map(value => `"${value}"`).join(",")
    );
    const csvContent = [csvHeaders, ...csvRows].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `hotel-reviews-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Reviews</CardTitle>
              <p className="text-sm text-muted-foreground">
                Monitor and respond to guest feedback
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="skeleton h-10 w-32" />
              <div className="skeleton h-10 w-32" />
              <div className="skeleton h-10 w-32" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="p-6 border rounded-lg">
              <div className="flex space-x-4">
                <div className="skeleton w-12 h-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="skeleton h-4 w-24" />
                    <div className="skeleton h-4 w-16" />
                    <div className="skeleton h-6 w-20 rounded-full" />
                  </div>
                  <div className="skeleton h-4 w-full" />
                  <div className="skeleton h-4 w-3/4" />
                  <div className="flex space-x-4">
                    <div className="skeleton h-3 w-16" />
                    <div className="skeleton h-3 w-16" />
                    <div className="skeleton h-3 w-16" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Reviews</CardTitle>
              <p className="text-sm text-muted-foreground">
                Monitor and respond to guest feedback
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Platform Filter */}
              <Select
                value={filters.platform}
                onValueChange={(value) => handleFilterChange("platform", value)}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="All Platforms" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Platforms</SelectItem>
                  <SelectItem value="google">Google Business</SelectItem>
                  <SelectItem value="tripadvisor">TripAdvisor</SelectItem>
                  <SelectItem value="booking">Booking.com</SelectItem>
                </SelectContent>
              </Select>

              {/* Rating Filter */}
              <Select
                value={filters.rating}
                onValueChange={(value) => handleFilterChange("rating", value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="All Ratings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Ratings</SelectItem>
                  <SelectItem value="5">5 Stars</SelectItem>
                  <SelectItem value="4">4 Stars</SelectItem>
                  <SelectItem value="3">3 Stars</SelectItem>
                  <SelectItem value="2">2 Stars</SelectItem>
                  <SelectItem value="1">1 Star</SelectItem>
                </SelectContent>
              </Select>

              {/* Reply Status Filter */}
              <Select
                value={filters.hasReply}
                onValueChange={(value) => handleFilterChange("hasReply", value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Reply Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="true">Replied</SelectItem>
                  <SelectItem value="false">No Reply</SelectItem>
                </SelectContent>
              </Select>

              <Button onClick={handleExportReviews} disabled={!reviews || reviews.length === 0}>
                Export Reviews
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {reviews && reviews.length > 0 ? (
            reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onReply={handleReply}
              />
            ))
          ) : (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No reviews found</p>
            </div>
          )}
        </CardContent>
      </Card>

      {selectedReviewId && (
        <ReplyModal
          reviewId={selectedReviewId}
          isOpen={!!selectedReviewId}
          onClose={() => setSelectedReviewId(null)}
          onSuccess={handleReplySuccess}
        />
      )}
    </>
  );
}
