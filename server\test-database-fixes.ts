import { DatabaseStorage } from './database';
import { googleBusinessAPI } from './services/google-business-api';
import { reviewAutomationService } from './services/review-automation';

/**
 * Test script to verify SQLite Date handling fixes and Google Business credential checking
 */
async function testDatabaseFixes() {
  console.log('🧪 Testing SQLite Date handling fixes...');
  
  try {
    const storage = new DatabaseStorage();
    
    // Test 1: Create a review with proper Date handling
    console.log('\n1. Testing review creation with Date objects...');
    const testReview = {
      hotelId: 1,
      platform: "google" as const,
      platformReviewId: `test_review_${Date.now()}`,
      guestName: "Test Guest",
      guestEmail: "<EMAIL>",
      rating: 4,
      title: "Test Review",
      content: "This is a test review to verify Date handling",
      reviewDate: new Date(), // This should be converted to ISO string
      roomNumber: "101",
      stayDuration: "2 nights",
      isVerifiedStay: true,
      metadata: { test: true },
      hasReply: false,
      needsAttention: false,
      priority: "normal" as const,
    };
    
    const createdReview = await storage.createReview(testReview);
    console.log('✅ Review created successfully with ID:', createdReview.id);
    
    // Test 2: Update review with Date objects
    console.log('\n2. Testing review update with Date objects...');
    const updatedReview = await storage.updateReview(createdReview.id, {
      reviewDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
    });
    console.log('✅ Review updated successfully');
    
    // Test 3: Create a reply with Date objects
    console.log('\n3. Testing reply creation with Date objects...');
    const testReply = {
      reviewId: createdReview.id,
      userId: 1,
      content: "Thank you for your review!",
      followUpAction: "none" as const,
      isPublished: true,
      publishedAt: new Date(), // This should be converted to ISO string
      platformReplyId: null,
    };
    
    const createdReply = await storage.createReply(testReply);
    console.log('✅ Reply created successfully with ID:', createdReply.id);
    
    // Test 4: Update sync status with Date objects
    console.log('\n4. Testing sync status update with Date objects...');
    const now = new Date();
    const nextSync = new Date(Date.now() + 30 * 60 * 1000);
    
    await storage.updateSyncStatus(1, "google", {
      status: "completed",
      lastSyncAt: now, // This should be converted to ISO string
      nextSyncAt: nextSync, // This should be converted to ISO string
      successCount: 1,
      lastError: null,
    });
    console.log('✅ Sync status updated successfully');
    
    // Test 5: Create platform token with Date objects
    console.log('\n5. Testing platform token creation with Date objects...');
    const testToken = {
      hotelId: 1,
      platform: "google" as const,
      accessToken: "test_access_token",
      refreshToken: "test_refresh_token",
      tokenType: "Bearer",
      expiresAt: new Date(Date.now() + 60 * 60 * 1000), // Expires in 1 hour
      scope: "test_scope",
      isActive: true,
      lastSyncAt: new Date(), // This should be converted to ISO string
    };
    
    const createdToken = await storage.createPlatformToken(testToken);
    console.log('✅ Platform token created successfully');
    
    console.log('\n🎉 All SQLite Date handling tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

async function testGoogleBusinessCredentials() {
  console.log('\n🔐 Testing Google Business credential checking...');
  
  try {
    // Test 1: Check credentials for hotel with no credentials
    console.log('\n1. Testing credential check for hotel without credentials...');
    const hasCredentials1 = await googleBusinessAPI.hasValidCredentials(999); // Non-existent hotel
    console.log('✅ Credential check returned:', hasCredentials1, '(expected: false)');
    
    // Test 2: Check credentials for hotel with credentials
    console.log('\n2. Testing credential check for hotel with credentials...');
    const hasCredentials2 = await googleBusinessAPI.hasValidCredentials(1);
    console.log('✅ Credential check returned:', hasCredentials2);
    
    console.log('\n🎉 All Google Business credential tests completed!');
    
  } catch (error) {
    console.error('❌ Credential test failed:', error);
    throw error;
  }
}

async function testAutomatedSyncRobustness() {
  console.log('\n🤖 Testing automated sync robustness...');
  
  try {
    // Get sync status to verify it's working
    const syncStatus = await reviewAutomationService.getSyncStatus();
    console.log('✅ Sync status retrieved:', syncStatus ? 'Found' : 'Not found');
    
    console.log('\n🎉 Automated sync robustness test completed!');
    
  } catch (error) {
    console.error('❌ Automated sync test failed:', error);
    throw error;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting comprehensive database and sync tests...\n');
  
  try {
    await testDatabaseFixes();
    await testGoogleBusinessCredentials();
    await testAutomatedSyncRobustness();
    
    console.log('\n🎊 All tests completed successfully!');
    console.log('\n📋 Summary of fixes applied:');
    console.log('   ✅ Fixed SQLite Date object handling - all dates now converted to ISO strings');
    console.log('   ✅ Added robust Google Business credential checking');
    console.log('   ✅ Enhanced automated sync with credential validation');
    console.log('   ✅ Added token refresh capability for expired tokens');
    console.log('   ✅ Improved error handling throughout the system');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
    process.exit(1);
  }
}

// Export for use in other files
export { runAllTests, testDatabaseFixes, testGoogleBusinessCredentials, testAutomatedSyncRobustness };

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}
