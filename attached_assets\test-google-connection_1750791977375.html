<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Business API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Google Business API Connection Test</h1>
    
    <div class="test-section">
        <h2>Step 1: Test Server Connection</h2>
        <button onclick="testServer()">Test Server</button>
        <div id="server-result"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test Google API Configuration</h2>
        <button onclick="testGoogleConfig()">Test Google Config</button>
        <div id="google-config-result"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Test Google OAuth Flow</h2>
        <button onclick="startGoogleAuth()">Start Google Auth</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>Debug Information</h2>
        <div id="debug-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Expected Server:</strong> http://localhost:5000</p>
        </div>
    </div>

    <script>
        document.getElementById('current-url').textContent = window.location.href;

        async function testServer() {
            const resultDiv = document.getElementById('server-result');
            resultDiv.innerHTML = '<p class="info">Testing server connection...</p>';
            
            try {
                const response = await fetch('/api/user');
                if (response.status === 401) {
                    resultDiv.innerHTML = '<p class="success">✅ Server is running (got expected 401 - not logged in)</p>';
                } else {
                    resultDiv.innerHTML = '<p class="success">✅ Server is running</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Server connection failed: ${error.message}</p>
                    <p class="info">Make sure you run: <code>npm run dev</code></p>`;
            }
        }

        async function testGoogleConfig() {
            const resultDiv = document.getElementById('google-config-result');
            resultDiv.innerHTML = '<p class="info">Testing Google API configuration...</p>';
            
            try {
                const response = await fetch('/api/google/test');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ Google API configured successfully</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p class="error">❌ Google API configuration error</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Failed to test Google config: ${error.message}</p>`;
            }
        }

        async function startGoogleAuth() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '<p class="info">Starting Google OAuth flow...</p>';
            
            try {
                const response = await fetch('/api/auth/google');
                const data = await response.json();
                
                if (response.ok && data.authUrl) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ Auth URL generated successfully</p>
                        <p><a href="${data.authUrl}" target="_blank" style="color: #007bff;">Click here to authenticate with Google</a></p>
                        <p class="info">Note: You need to be logged in to your hotel dashboard first</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p class="error">❌ Failed to generate auth URL</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Auth flow failed: ${error.message}</p>`;
            }
        }

        // Auto-test server on page load
        window.onload = function() {
            testServer();
        };
    </script>
</body>
</html>
