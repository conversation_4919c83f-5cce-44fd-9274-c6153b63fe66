import { pgTable, text, serial, integer, boolean, timestamp, decimal, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table for authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  role: text("role").notNull().default("staff"), // admin, manager, staff
  hotelId: integer("hotel_id"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Hotels table
export const hotels = pgTable("hotels", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address"),
  city: text("city"),
  country: text("country"),
  phoneNumber: text("phone_number"),
  email: text("email"),
  website: text("website"),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Reviews table - centralized storage for all platforms
export const reviews = pgTable("reviews", {
  id: serial("id").primaryKey(),
  hotelId: integer("hotel_id").notNull(),
  platform: text("platform").notNull(), // google, tripadvisor, booking
  platformReviewId: text("platform_review_id").notNull(),
  guestName: text("guest_name").notNull(),
  guestEmail: text("guest_email"),
  rating: integer("rating").notNull(), // 1-5 stars
  title: text("title"),
  content: text("content").notNull(),
  reviewDate: timestamp("review_date").notNull(),
  roomNumber: text("room_number"),
  stayDuration: text("stay_duration"),
  isVerifiedStay: boolean("is_verified_stay").default(false),
  metadata: jsonb("metadata"), // platform-specific data
  hasReply: boolean("has_reply").default(false),
  needsAttention: boolean("needs_attention").default(false),
  priority: text("priority").default("normal"), // low, normal, high, urgent
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Replies table for staff responses
export const replies = pgTable("replies", {
  id: serial("id").primaryKey(),
  reviewId: integer("review_id").notNull(),
  userId: integer("user_id").notNull(), // staff member who replied
  content: text("content").notNull(),
  isPublished: boolean("is_published").default(false),
  publishedAt: timestamp("published_at"),
  platformReplyId: text("platform_reply_id"), // ID from the platform
  followUpAction: text("follow_up_action"), // none, contact_guest, compensation, schedule_followup
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Platform tokens for API access
export const platformTokens = pgTable("platform_tokens", {
  id: serial("id").primaryKey(),
  hotelId: integer("hotel_id").notNull(),
  platform: text("platform").notNull(), // google, tripadvisor, booking
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  tokenType: text("token_type").default("Bearer"),
  expiresAt: timestamp("expires_at"),
  scope: text("scope"),
  isActive: boolean("is_active").default(true),
  lastSyncAt: timestamp("last_sync_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Google Business Profile locations
export const googleBusinessLocations = pgTable("google_business_locations", {
  id: serial("id").primaryKey(),
  hotelId: integer("hotel_id").notNull(),
  locationId: text("location_id").notNull(), // Google's location ID
  accountId: text("account_id").notNull(), // Google Business account ID
  name: text("name").notNull(),
  address: text("address"),
  phoneNumber: text("phone_number"),
  website: text("website"),
  averageRating: decimal("average_rating", { precision: 3, scale: 2 }),
  totalReviewCount: integer("total_review_count").default(0),
  isVerified: boolean("is_verified").default(false),
  status: text("status").default("active"), // active, inactive, suspended
  lastSyncAt: timestamp("last_sync_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Sync status tracking
export const syncStatus = pgTable("sync_status", {
  id: serial("id").primaryKey(),
  hotelId: integer("hotel_id").notNull(),
  platform: text("platform").notNull(),
  lastSyncAt: timestamp("last_sync_at"),
  nextSyncAt: timestamp("next_sync_at"),
  status: text("status").notNull().default("pending"), // pending, running, completed, failed
  recordsProcessed: integer("records_processed").default(0),
  recordsAdded: integer("records_added").default(0),
  recordsUpdated: integer("records_updated").default(0),
  errorMessage: text("error_message"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Insert schemas for validation
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertHotelSchema = createInsertSchema(hotels).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertReviewSchema = createInsertSchema(reviews).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertReplySchema = createInsertSchema(replies).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertPlatformTokenSchema = createInsertSchema(platformTokens).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertGoogleBusinessLocationSchema = createInsertSchema(googleBusinessLocations).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Login schema
export const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

// Reply schema with validation
export const replySchema = z.object({
  content: z.string().min(10, "Reply must be at least 10 characters").max(1000, "Reply cannot exceed 1000 characters"),
  followUpAction: z.enum(["none", "contact_guest", "compensation", "schedule_followup"]).optional(),
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Hotel = typeof hotels.$inferSelect;
export type InsertHotel = z.infer<typeof insertHotelSchema>;
export type Review = typeof reviews.$inferSelect;
export type InsertReview = z.infer<typeof insertReviewSchema>;
export type Reply = typeof replies.$inferSelect;
export type InsertReply = z.infer<typeof insertReplySchema>;
export type PlatformToken = typeof platformTokens.$inferSelect;
export type InsertPlatformToken = z.infer<typeof insertPlatformTokenSchema>;
export type GoogleBusinessLocation = typeof googleBusinessLocations.$inferSelect;
export type InsertGoogleBusinessLocation = z.infer<typeof insertGoogleBusinessLocationSchema>;
export type SyncStatus = typeof syncStatus.$inferSelect;
export type LoginRequest = z.infer<typeof loginSchema>;
export type ReplyRequest = z.infer<typeof replySchema>;

// Statistics types
export type ReviewStats = {
  totalReviews: number;
  averageRating: number;
  pendingReplies: number;
  responseRate: number;
  ratingDistribution: Record<string, number>;
  monthlyTrend: Array<{ month: string; count: number; rating: number }>;
};

export type PlatformConnection = {
  platform: string;
  connected: boolean;
  reviewCount: number;
  lastSync: string | null;
  status: 'active' | 'inactive' | 'error';
};
