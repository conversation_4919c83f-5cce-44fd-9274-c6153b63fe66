import { apiRequest } from "@/lib/queryClient";
import type { ReplyRequest } from "@shared/schema";

export const api = {
  // Reviews
  getReviews: async (filters?: { platform?: string; rating?: number; hasReply?: boolean; needsAttention?: boolean }) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }
    const response = await apiRequest("GET", `/api/reviews?${params}`);
    return response.json();
  },

  getReview: async (id: number) => {
    const response = await apiRequest("GET", `/api/reviews/${id}`);
    return response.json();
  },

  replyToReview: async (id: number, reply: ReplyRequest) => {
    const response = await apiRequest("POST", `/api/reviews/${id}/reply`, reply);
    return response.json();
  },

  getReplies: async (reviewId: number) => {
    const response = await apiRequest("GET", `/api/reviews/${reviewId}/replies`);
    return response.json();
  },

  // Statistics
  getStatistics: async () => {
    const response = await apiRequest("GET", "/api/statistics");
    return response.json();
  },

  // Platforms
  getPlatformConnections: async () => {
    const response = await apiRequest("GET", "/api/platforms/connected");
    return response.json();
  },

  // Google Business
  testGoogleAPI: async () => {
    const response = await apiRequest("GET", "/api/google/test");
    return response.json();
  },

  getGoogleAuthUrl: async () => {
    const response = await apiRequest("GET", "/api/auth/google");
    return response.json();
  },

  getGoogleAccounts: async () => {
    const response = await apiRequest("GET", "/api/google/accounts");
    return response.json();
  },

  getGoogleLocations: async (accountName: string) => {
    const response = await apiRequest("GET", `/api/google/locations?accountName=${encodeURIComponent(accountName)}`);
    return response.json();
  },

  syncGoogleReviews: async () => {
    const response = await apiRequest("POST", "/api/google/sync-reviews");
    return response.json();
  },

  // Hotel
  getHotel: async () => {
    const response = await apiRequest("GET", "/api/hotel");
    return response.json();
  },

  // Activity
  getActivity: async () => {
    const response = await apiRequest("GET", "/api/activity");
    return response.json();
  },

  // Generic request method for new API endpoints
  request: async (url: string, options: RequestInit = {}) => {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(error.message || 'Request failed');
    }
    return response.json();
  },
};
