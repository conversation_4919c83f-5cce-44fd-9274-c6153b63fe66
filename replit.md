# Hotel Review Management System

## Overview

This is a full-stack hotel review management application built with React, TypeScript, Node.js, and Express. The application helps hotels monitor, manage, and respond to customer reviews across multiple platforms including Google Business Profile, TripAdvisor, and Booking.com. It features a modern dashboard interface with authentication, analytics, and automated review synchronization capabilities.

## System Architecture

The application follows a full-stack monorepo structure with clear separation between client-side and server-side code:

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query (React Query) for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod validation
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Passport.js with session-based auth
- **Session Storage**: MemoryStore for development, configurable for production

## Key Components

### Data Layer
- **Database**: SQLite database with comprehensive schema for production use
- **Tables**: users, hotels, reviews, replies, platform_tokens, google_business_locations, sync_status
- **Features**: Automated data seeding, foreign key constraints, indexes for performance
- **File Exports**: Real-time text file generation with timestamped backups

### Authentication & Authorization
- **Strategy**: Session-based authentication using Passport.js
- **User Roles**: Admin, manager, and staff roles with different permission levels
- **Security**: Secure session management with configurable secrets

### External Integrations
- **Google Business Profile API**: OAuth 2.0 integration with automated sync every 30 minutes
- **Review Automation**: Real-time processing with auto-replies for negative reviews
- **Text File Export**: Automated export to current_reviews.txt and timestamped backups
- **Platform Support**: Extensible architecture for additional platforms

### User Interface
- **Dashboard**: Real-time overview of review statistics and platform status
- **Review Management**: Comprehensive table with filtering, sorting, and bulk actions
- **Analytics**: Charts and insights using Recharts library
- **Response System**: Modal-based reply interface with templates

## Data Flow

1. **Authentication Flow**: User logs in via username/password → Passport validates credentials → Session established → User data cached in React Query
2. **Review Synchronization**: Platform APIs polled → New reviews stored in database → UI updated via React Query invalidation
3. **Reply Management**: User composes reply → Validated with Zod schemas → Sent to platform API → Database updated → UI refreshed

## External Dependencies

### Core Dependencies
- **Database**: PostgreSQL (local or cloud-hosted)
- **Authentication**: Google OAuth 2.0 for Business Profile integration
- **UI Components**: Radix UI primitives via shadcn/ui
- **Charts**: Recharts for analytics visualization

### Development Tools
- **TypeScript**: Full type safety across frontend and backend
- **Drizzle Kit**: Database schema management and migrations
- **ESBuild**: Production bundling for server code
- **PostCSS**: CSS processing with Tailwind

## Deployment Strategy

### Development Environment
- **Runtime**: Node.js 20 with Replit modules
- **Database**: PostgreSQL 16 local instance
- **Hot Reload**: Vite HMR for frontend, tsx for backend development
- **Port Configuration**: Application serves on port 5000

### Production Build
- **Frontend**: Vite builds static assets to `dist/public`
- **Backend**: ESBuild bundles server code to `dist/index.js`
- **Deployment**: Configured for Replit autoscale deployment
- **Environment**: Production-specific configurations via NODE_ENV

### Environment Configuration
- Session secrets and API keys via environment variables
- Database connections via DATABASE_URL
- Google OAuth credentials for platform integration
- Configurable for different deployment targets

## Changelog

```
Changelog:
- June 24, 2025. Initial setup
- June 24, 2025. Implemented SQLite database with comprehensive schema
- June 24, 2025. Added automated Google Business review sync every 30 minutes
- June 24, 2025. Created real-time text file export functionality
- June 24, 2025. Built auto-reply system for negative reviews (≤3 stars)
- June 24, 2025. Added automation management interface with start/stop controls
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```