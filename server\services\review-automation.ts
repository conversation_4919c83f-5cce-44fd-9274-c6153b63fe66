import { googleBusinessAPI } from './google-business-api';
import { storage } from '../storage';
import * as fs from 'fs';
import * as path from 'path';

export class ReviewAutomationService {
  private syncInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor() {
    this.startAutomatedSync();
  }

  startAutomatedSync() {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log('Starting automated review sync service...');

    // Sync every 30 minutes
    this.syncInterval = setInterval(async () => {
      try {
        await this.performAutomatedSync();
      } catch (error) {
        console.error('Error in automated sync interval:', error);
      }
    }, 30 * 60 * 1000); // 30 minutes

    // Run initial sync after 10 seconds
    setTimeout(async () => {
      try {
        await this.performAutomatedSync();
      } catch (error) {
        console.error('Error in initial automated sync:', error);
      }
    }, 10000);
  }

  stopAutomatedSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isRunning = false;
    console.log('Stopped automated review sync service');
  }

  private async performAutomatedSync() {
    try {
      console.log('Starting automated review sync...');

      // Get all hotels (in this case, we have one hotel with ID 1)
      const hotel = await storage.getHotel(1);
      if (!hotel) {
        console.log('No hotel found for automated sync');
        return;
      }

      // Check if Google Business credentials are available before attempting sync
      const hasCredentials = await googleBusinessAPI.hasValidCredentials(hotel.id);
      if (!hasCredentials) {
        console.log(`No valid Google Business credentials found for hotel ${hotel.id}. Skipping automated sync.`);

        // Update sync status to indicate missing credentials
        await storage.updateSyncStatus(hotel.id, 'google', {
          status: "failed",
          lastError: "No valid Google Business Profile credentials found",
          errorCount: (await storage.getSyncStatus(hotel.id, 'google'))?.errorCount || 0 + 1,
        });
        return;
      }

      // Sync reviews from Google Business
      const result = await googleBusinessAPI.syncReviews(hotel.id);
      console.log(`Automated sync completed: ${result.added} new reviews, ${result.updated} updated`);

      if (result.added > 0 || result.updated > 0) {
        // Export to text file after sync
        await this.exportReviewsToTextFile(hotel.id);

        // Auto-reply to new negative reviews (rating <= 3)
        await this.autoReplyToNegativeReviews(hotel.id);
      }

      // Update sync status - use proper Date handling
      const now = new Date();
      const nextSync = new Date(Date.now() + 30 * 60 * 1000); // Next sync in 30 minutes
      const currentStatus = await storage.getSyncStatus(hotel.id, 'google');

      await storage.updateSyncStatus(hotel.id, 'google', {
        status: "completed",
        lastSyncAt: now,
        nextSyncAt: nextSync,
        successCount: (currentStatus?.successCount || 0) + 1,
        lastError: null,
      });

    } catch (error) {
      console.error('Error in automated sync:', error);

      // Update error in sync status
      const hotel = await storage.getHotel(1);
      if (hotel) {
        const currentStatus = await storage.getSyncStatus(hotel.id, 'google');
        await storage.updateSyncStatus(hotel.id, 'google', {
          status: "failed",
          lastError: error instanceof Error ? error.message : 'Unknown error',
          errorCount: (currentStatus?.errorCount || 0) + 1,
        });
      }
    }
  }

  private async exportReviewsToTextFile(hotelId: number) {
    try {
      const reviews = await storage.getReviews({ hotelId });
      const hotel = await storage.getHotel(hotelId);
      
      // Create reviews directory if it doesn't exist
      const reviewsDir = './reviews_exports';
      if (!fs.existsSync(reviewsDir)) {
        fs.mkdirSync(reviewsDir, { recursive: true });
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `reviews_${hotel?.name?.replace(/\s+/g, '_')}_${timestamp}.txt`;
      const filePath = path.join(reviewsDir, filename);
      
      let content = `GOOGLE BUSINESS REVIEWS - REAL TIME EXPORT\n`;
      content += `Hotel: ${hotel?.name || 'Unknown'}\n`;
      content += `Export Date: ${new Date().toLocaleString()}\n`;
      content += `Total Reviews: ${reviews.length}\n`;
      content += `Average Rating: ${await this.calculateAverageRating(hotelId)}\n`;
      content += `${'='.repeat(100)}\n\n`;
      
      // Sort reviews by date (newest first)
      const sortedReviews = reviews.sort((a, b) => b.reviewDate.getTime() - a.reviewDate.getTime());
      
      for (const review of sortedReviews) {
        const replies = await storage.getRepliesByReviewId(review.id);
        
        content += `REVIEW #${review.id}\n`;
        content += `Platform: ${review.platform.toUpperCase()}\n`;
        content += `Guest Name: ${review.guestName}\n`;
        content += `Rating: ${'★'.repeat(review.rating)}${'☆'.repeat(5 - review.rating)} (${review.rating}/5)\n`;
        content += `Review Date: ${review.reviewDate.toLocaleString()}\n`;
        content += `Room Number: ${review.roomNumber || 'Not specified'}\n`;
        content += `Stay Duration: ${review.stayDuration || 'Not specified'}\n`;
        content += `Priority: ${review.priority?.toUpperCase() || 'NORMAL'}\n`;
        content += `Needs Attention: ${review.needsAttention ? 'YES' : 'NO'}\n`;
        content += `\nReview Content:\n${review.content}\n`;
        
        if (replies.length > 0) {
          content += `\nHOTEL REPLIES (${replies.length}):\n`;
          replies.forEach((reply, index) => {
            content += `\nReply ${index + 1}:\n`;
            content += `Response Date: ${reply.publishedAt?.toLocaleString() || 'Draft'}\n`;
            content += `Status: ${reply.isPublished ? 'PUBLISHED' : 'DRAFT'}\n`;
            content += `Follow-up Action: ${reply.followUpAction?.toUpperCase() || 'NONE'}\n`;
            content += `Content: ${reply.content}\n`;
          });
        } else {
          content += `\nSTATUS: ⚠️ NO REPLY YET - REQUIRES RESPONSE\n`;
        }
        
        content += `\n${'-'.repeat(100)}\n\n`;
      }
      
      // Add summary at the end
      content += `\nSUMMARY REPORT\n`;
      content += `${'='.repeat(50)}\n`;
      content += `Total Reviews: ${reviews.length}\n`;
      content += `5-Star Reviews: ${reviews.filter(r => r.rating === 5).length}\n`;
      content += `4-Star Reviews: ${reviews.filter(r => r.rating === 4).length}\n`;
      content += `3-Star Reviews: ${reviews.filter(r => r.rating === 3).length}\n`;
      content += `2-Star Reviews: ${reviews.filter(r => r.rating === 2).length}\n`;
      content += `1-Star Reviews: ${reviews.filter(r => r.rating === 1).length}\n`;
      content += `Reviews with Replies: ${reviews.filter(r => r.hasReply).length}\n`;
      content += `Reviews Needing Attention: ${reviews.filter(r => r.needsAttention).length}\n`;
      content += `Response Rate: ${reviews.length > 0 ? Math.round((reviews.filter(r => r.hasReply).length / reviews.length) * 100) : 0}%\n`;
      
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Reviews exported to: ${filePath}`);
      
      // Also update the main export file
      fs.writeFileSync('./current_reviews.txt', content, 'utf8');
      
      return filePath;
    } catch (error) {
      console.error('Error exporting reviews to text file:', error);
      throw error;
    }
  }

  private async calculateAverageRating(hotelId: number): Promise<string> {
    const reviews = await storage.getReviews({ hotelId });
    if (reviews.length === 0) return '0.0';
    
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  }

  private async autoReplyToNegativeReviews(hotelId: number) {
    try {
      // Get reviews that need attention and don't have replies
      const reviews = await storage.getReviews({ 
        hotelId, 
        hasReply: false, 
        needsAttention: true 
      });
      
      const admin = await storage.getUserByUsername('admin');
      if (!admin) return;

      for (const review of reviews) {
        if (review.rating <= 3) {
          // Generate appropriate auto-reply based on rating and content
          let autoReplyContent = '';
          
          if (review.rating === 1) {
            autoReplyContent = `Dear ${review.guestName}, we sincerely apologize for your disappointing experience. Your feedback is extremely valuable to us, and we take all concerns very seriously. We would appreciate the opportunity to discuss this matter directly with you to understand what went wrong and make it right. Please contact us at your earliest convenience so we can address these issues immediately and ensure this never happens again.`;
          } else if (review.rating === 2) {
            autoReplyContent = `Dear ${review.guestName}, thank you for taking the time to share your feedback. We apologize that your stay did not meet your expectations. We take all guest concerns seriously and are committed to improving our services. We would love the opportunity to discuss your experience further and make necessary improvements. Please reach out to us directly so we can address your concerns.`;
          } else if (review.rating === 3) {
            autoReplyContent = `Dear ${review.guestName}, thank you for your honest feedback. We appreciate you taking the time to share your experience with us. While we're glad you had some positive aspects during your stay, we understand there were areas where we could improve. We value your input and are continuously working to enhance our services. We hope to welcome you back for a better experience in the future.`;
          }

          // Create auto-reply
          await storage.createReply({
            reviewId: review.id,
            userId: admin.id,
            content: autoReplyContent,
            followUpAction: review.rating <= 2 ? 'contact_guest' : 'none',
            isPublished: true,
            publishedAt: new Date(),
            platformReplyId: `auto_reply_${Date.now()}_${review.id}`,
          });

          console.log(`Auto-reply created for review ${review.id} (${review.rating}-star review from ${review.guestName})`);
        }
      }
    } catch (error) {
      console.error('Error in auto-reply service:', error);
    }
  }

  // Manual export method
  async exportCurrentReviews(hotelId: number): Promise<string> {
    return await this.exportReviewsToTextFile(hotelId);
  }

  // Get sync status
  async getSyncStatus() {
    const hotel = await storage.getHotel(1);
    if (!hotel) return null;
    
    return await storage.getSyncStatus(hotel.id, 'google');
  }
}

export const reviewAutomationService = new ReviewAutomationService();