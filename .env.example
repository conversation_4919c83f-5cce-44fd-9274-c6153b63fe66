# Application Configuration
SESSION_SECRET="your-secure-session-secret-change-this-in-production"

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/hotel_reviews"

# Google Business Profile API Configuration
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:5000/api/auth/google/callback"
GOOGLE_API_SCOPES="https://www.googleapis.com/auth/business.manage"

# Environment
NODE_ENV="development"

# Optional: Additional Platform API Keys (for future integrations)
# TRIPADVISOR_API_KEY="your-tripadvisor-api-key"
# BOOKING_API_KEY="your-booking-api-key"
