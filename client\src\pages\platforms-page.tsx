import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { TopBar } from "@/components/TopBar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CheckCircle, AlertCircle, Clock, ExternalLink, RefreshCw, Settings } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import type { PlatformConnection } from "@shared/schema";

export default function PlatformsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: connections, isLoading } = useQuery<PlatformConnection[]>({
    queryKey: ["/api/platforms/connected"],
    queryFn: api.getPlatformConnections,
  });

  const connectGoogleMutation = useMutation({
    mutationFn: api.getGoogleAuthUrl,
    onSuccess: (data) => {
      window.location.href = data.authUrl;
    },
    onError: (error: any) => {
      toast({
        title: "Connection failed",
        description: error.message || "Failed to connect to Google Business Profile",
        variant: "destructive",
      });
    },
  });

  const syncMutation = useMutation({
    mutationFn: api.syncGoogleReviews,
    onSuccess: (data) => {
      toast({
        title: "Sync completed",
        description: `Added ${data.added} new reviews, updated ${data.updated} existing reviews.`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/platforms/connected"] });
      queryClient.invalidateQueries({ queryKey: ["/api/reviews"] });
    },
    onError: (error: any) => {
      toast({
        title: "Sync failed",
        description: error.message || "Failed to sync reviews",
        variant: "destructive",
      });
    },
  });

  const testGoogleMutation = useMutation({
    mutationFn: api.testGoogleAPI,
    onSuccess: (data) => {
      toast({
        title: data.success ? "Test successful" : "Test failed",
        description: data.message,
        variant: data.success ? "default" : "destructive",
      });
    },
  });

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "google":
        return <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-red-500 rounded" />;
      case "tripadvisor":
        return (
          <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-bold">T</span>
          </div>
        );
      case "booking":
        return (
          <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-sm font-bold">B</span>
          </div>
        );
      default:
        return null;
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case "google":
        return "Google Business Profile";
      case "tripadvisor":
        return "TripAdvisor";
      case "booking":
        return "Booking.com";
      default:
        return platform;
    }
  };

  const getPlatformDescription = (platform: string) => {
    switch (platform) {
      case "google":
        return "Manage reviews from Google My Business and Google Maps";
      case "tripadvisor":
        return "Sync reviews from TripAdvisor (Coming Soon)";
      case "booking":
        return "Import guest reviews from Booking.com (Coming Soon)";
      default:
        return "";
    }
  };

  const getStatusIcon = (connection: PlatformConnection) => {
    if (!connection.connected) {
      return <AlertCircle className="w-5 h-5 text-muted-foreground" />;
    }
    
    switch (connection.status) {
      case "active":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "error":
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Clock className="w-5 h-5 text-amber-600" />;
    }
  };

  const handleConnect = (platform: string) => {
    if (platform === "google") {
      connectGoogleMutation.mutate();
    } else {
      toast({
        title: "Coming Soon",
        description: `${getPlatformName(platform)} integration is coming soon!`,
      });
    }
  };

  const handleSync = (platform: string) => {
    if (platform === "google") {
      syncMutation.mutate();
    }
  };

  const handleTest = (platform: string) => {
    if (platform === "google") {
      testGoogleMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopBar
            onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
            title="Platform Connections"
            subtitle="Manage your review platform integrations"
          />
          <main className="flex-1 overflow-auto p-6">
            <div className="space-y-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="skeleton h-20 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />

      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          title="Platform Connections"
          subtitle="Manage your review platform integrations"
        />

        <main className="flex-1 overflow-auto p-6 custom-scrollbar">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Platform Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Connected Platforms</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Overview of your review platform integrations
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {connections?.map((connection) => (
                    <div
                      key={connection.platform}
                      className={`p-4 rounded-lg border-2 transition-colors ${
                        connection.connected
                          ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20"
                          : "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800"
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-2">
                        {getPlatformIcon(connection.platform)}
                        <div>
                          <h3 className="font-medium text-foreground">
                            {getPlatformName(connection.platform)}
                          </h3>
                          <p className="text-xs text-muted-foreground">
                            {connection.connected ? `${connection.reviewCount} reviews` : "Not connected"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        {getStatusIcon(connection)}
                        <Badge variant={connection.connected ? "default" : "secondary"}>
                          {connection.connected ? "Connected" : "Disconnected"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Platform Details */}
            <div className="space-y-6">
              {connections?.map((connection) => (
                <Card key={connection.platform}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-sm border">
                          {getPlatformIcon(connection.platform)}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-foreground mb-1">
                            {getPlatformName(connection.platform)}
                          </h3>
                          <p className="text-sm text-muted-foreground mb-3">
                            {getPlatformDescription(connection.platform)}
                          </p>
                          
                          {connection.connected && (
                            <div className="space-y-2">
                              <div className="flex items-center space-x-4 text-sm">
                                <span className="text-muted-foreground">Reviews:</span>
                                <span className="font-medium">{connection.reviewCount}</span>
                              </div>
                              {connection.lastSync && (
                                <div className="flex items-center space-x-4 text-sm">
                                  <span className="text-muted-foreground">Last sync:</span>
                                  <span className="font-medium">
                                    {new Date(connection.lastSync).toLocaleString()}
                                  </span>
                                </div>
                              )}
                              <div className="flex items-center space-x-4 text-sm">
                                <span className="text-muted-foreground">Status:</span>
                                <div className="flex items-center space-x-2">
                                  {getStatusIcon(connection)}
                                  <span className="font-medium capitalize">{connection.status}</span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2">
                        {connection.connected ? (
                          <>
                            {connection.platform === "google" && (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleSync(connection.platform)}
                                  disabled={syncMutation.isPending}
                                  className="w-32"
                                >
                                  {syncMutation.isPending ? (
                                    <>
                                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                      Syncing...
                                    </>
                                  ) : (
                                    <>
                                      <RefreshCw className="w-4 h-4 mr-2" />
                                      Sync Now
                                    </>
                                  )}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleTest(connection.platform)}
                                  disabled={testGoogleMutation.isPending}
                                  className="w-32"
                                >
                                  <Settings className="w-4 h-4 mr-2" />
                                  Test API
                                </Button>
                              </>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              className="w-32"
                            >
                              <ExternalLink className="w-4 h-4 mr-2" />
                              Manage
                            </Button>
                          </>
                        ) : (
                          <Button
                            size="sm"
                            onClick={() => handleConnect(connection.platform)}
                            disabled={connectGoogleMutation.isPending}
                            className="w-32"
                          >
                            {connectGoogleMutation.isPending ? "Connecting..." : "Connect"}
                          </Button>
                        )}
                      </div>
                    </div>

                    {connection.platform === "google" && connection.connected && (
                      <>
                        <Separator className="my-4" />
                        <div className="space-y-3">
                          <h4 className="font-medium text-foreground">Google Business Profile Setup</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>OAuth authentication configured</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>API permissions granted</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>Business location verified</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>Review sync enabled</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span>Reply posting enabled</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Clock className="w-4 h-4 text-amber-600" />
                                <span>Auto-sync every 30 minutes</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Help Section */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Setting up Google Business Profile
                  </h4>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                    To connect your Google Business Profile, you'll need to ensure your business is verified and you have management access.
                  </p>
                  <Button size="sm" variant="outline" className="bg-white dark:bg-gray-800">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Setup Guide
                  </Button>
                </div>
                
                <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                  <h4 className="font-medium text-amber-900 dark:text-amber-100 mb-2">
                    Platform Integration Status
                  </h4>
                  <ul className="text-sm text-amber-800 dark:text-amber-200 space-y-1">
                    <li>• Google Business Profile - ✅ Available</li>
                    <li>• TripAdvisor - 🚧 Coming Soon</li>
                    <li>• Booking.com - 🚧 Coming Soon</li>
                    <li>• Expedia - 📋 Planned</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
