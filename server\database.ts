import Database from 'better-sqlite3';
import { 
  users, hotels, reviews, replies, platformTokens, googleBusinessLocations, syncStatus,
  type User, type InsertUser, type Hotel, type InsertHotel, 
  type Review, type InsertReview, type Reply, type InsertReply,
  type PlatformToken, type InsertPlatformToken, type GoogleBusinessLocation,
  type InsertGoogleBusinessLocation, type SyncStatus, type ReviewStats, type PlatformConnection
} from "@shared/schema";
import type { IStorage } from "./storage";
import * as fs from 'fs';
import * as path from 'path';

export class SQLiteStorage implements IStorage {
  private db: Database.Database;

  constructor(dbPath: string = './hotel_reviews.db') {
    this.db = new Database(dbPath);
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('foreign_keys = ON');
    this.initializeTables();
    this.seedData();
  }

  private initializeTables() {
    // Create tables
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'staff',
        hotelId INTEGER,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (hotelId) REFERENCES hotels(id)
      );

      CREATE TABLE IF NOT EXISTS hotels (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        city TEXT,
        country TEXT,
        phoneNumber TEXT,
        email TEXT,
        website TEXT,
        description TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS reviews (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        hotelId INTEGER NOT NULL,
        platform TEXT NOT NULL,
        platformReviewId TEXT NOT NULL,
        guestName TEXT NOT NULL,
        guestEmail TEXT,
        rating INTEGER NOT NULL,
        title TEXT,
        content TEXT NOT NULL,
        reviewDate DATETIME NOT NULL,
        roomNumber TEXT,
        stayDuration TEXT,
        isVerifiedStay BOOLEAN,
        metadata TEXT,
        hasReply BOOLEAN DEFAULT FALSE,
        needsAttention BOOLEAN DEFAULT FALSE,
        priority TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (hotelId) REFERENCES hotels(id),
        UNIQUE(platform, platformReviewId)
      );

      CREATE TABLE IF NOT EXISTS replies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        reviewId INTEGER NOT NULL,
        userId INTEGER NOT NULL,
        content TEXT NOT NULL,
        followUpAction TEXT,
        isPublished BOOLEAN DEFAULT FALSE,
        publishedAt DATETIME,
        platformReplyId TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (reviewId) REFERENCES reviews(id),
        FOREIGN KEY (userId) REFERENCES users(id)
      );

      CREATE TABLE IF NOT EXISTS platform_tokens (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        hotelId INTEGER NOT NULL,
        platform TEXT NOT NULL,
        accessToken TEXT,
        refreshToken TEXT,
        tokenType TEXT,
        expiresAt DATETIME,
        scope TEXT,
        isActive BOOLEAN DEFAULT TRUE,
        lastSyncAt DATETIME,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (hotelId) REFERENCES hotels(id),
        UNIQUE(hotelId, platform)
      );

      CREATE TABLE IF NOT EXISTS google_business_locations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        hotelId INTEGER NOT NULL,
        locationId TEXT NOT NULL,
        accountId TEXT NOT NULL,
        name TEXT NOT NULL,
        address TEXT,
        phoneNumber TEXT,
        website TEXT,
        averageRating TEXT,
        totalReviewCount INTEGER,
        isVerified BOOLEAN,
        status TEXT,
        lastSyncAt DATETIME,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (hotelId) REFERENCES hotels(id),
        UNIQUE(hotelId, locationId)
      );

      CREATE TABLE IF NOT EXISTS sync_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        hotelId INTEGER NOT NULL,
        platform TEXT NOT NULL,
        lastSyncAt DATETIME,
        nextSyncAt DATETIME,
        syncFrequency INTEGER DEFAULT 30,
        isActive BOOLEAN DEFAULT TRUE,
        lastError TEXT,
        successCount INTEGER DEFAULT 0,
        errorCount INTEGER DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (hotelId) REFERENCES hotels(id),
        UNIQUE(hotelId, platform)
      );
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_reviews_hotel_platform ON reviews(hotelId, platform);
      CREATE INDEX IF NOT EXISTS idx_reviews_date ON reviews(reviewDate DESC);
      CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);
      CREATE INDEX IF NOT EXISTS idx_reviews_needs_attention ON reviews(needsAttention);
      CREATE INDEX IF NOT EXISTS idx_replies_review ON replies(reviewId);
      CREATE INDEX IF NOT EXISTS idx_platform_tokens_hotel ON platform_tokens(hotelId, platform);
    `);
  }

  private seedData() {
    const hotelExists = this.db.prepare('SELECT COUNT(*) as count FROM hotels').get() as { count: number };
    if (hotelExists.count > 0) return; // Data already seeded

    // Seed hotel
    const insertHotel = this.db.prepare(`
      INSERT INTO hotels (name, address, city, country, phoneNumber, email, website, description)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const hotelId = insertHotel.run(
      "Grand Palace Hotel",
      "123 Luxury Avenue", 
      "New York",
      "USA",
      "******-0123",
      "<EMAIL>",
      "https://grandpalacehotel.com",
      "A luxury hotel in the heart of the city"
    ).lastInsertRowid as number;

    // Seed admin user
    const insertUser = this.db.prepare(`
      INSERT INTO users (username, password, email, firstName, lastName, role, hotelId)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const userId = insertUser.run(
      "admin",
      "$2b$10$example", // In real app, hash the password
      "<EMAIL>",
      "Sarah",
      "Johnson", 
      "manager",
      hotelId
    ).lastInsertRowid as number;

    // Seed sample reviews
    const insertReview = this.db.prepare(`
      INSERT INTO reviews (hotelId, platform, platformReviewId, guestName, rating, content, reviewDate, roomNumber, stayDuration, isVerifiedStay, hasReply, needsAttention, priority)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const reviewsData = [
      {
        guestName: "Michael Chen",
        rating: 5,
        content: "Exceptional service and beautiful rooms. The staff went above and beyond to make our anniversary celebration special.",
        roomNumber: "1205",
        stayDuration: "3-night stay",
        reviewDate: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        hasReply: false,
        needsAttention: false,
        priority: "normal",
      },
      {
        guestName: "Emma Rodriguez", 
        rating: 4,
        content: "Great location and friendly staff. The room was clean and comfortable. Only issue was the room service took longer than expected.",
        roomNumber: "823",
        stayDuration: "2-night stay", 
        reviewDate: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        hasReply: true,
        needsAttention: false,
        priority: "normal",
      },
      {
        guestName: "David Park",
        rating: 3,
        content: "The hotel has potential but needs improvement. Room 312 had a broken air conditioning unit and maintenance took 3 hours to fix it.",
        roomNumber: "312", 
        stayDuration: "4-night stay",
        reviewDate: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        hasReply: false,
        needsAttention: true,
        priority: "high",
      }
    ];

    reviewsData.forEach((review, index) => {
      const reviewId = insertReview.run(
        hotelId,
        "google",
        `google_${Date.now()}_${index}`,
        review.guestName,
        review.rating,
        review.content,
        review.reviewDate,
        review.roomNumber,
        review.stayDuration,
        true,
        review.hasReply,
        review.needsAttention,
        review.priority
      ).lastInsertRowid as number;

      // Add replies for reviews that have them
      if (review.hasReply) {
        const insertReply = this.db.prepare(`
          INSERT INTO replies (reviewId, userId, content, isPublished, publishedAt, followUpAction)
          VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        const replyContent = review.rating >= 4 
          ? "Thank you so much for your wonderful review! We're thrilled to hear about your positive experience."
          : "Thank you for your feedback. We appreciate you taking the time to share your experience and are working to improve.";

        insertReply.run(
          reviewId,
          userId,
          replyContent,
          true,
          new Date(new Date(review.reviewDate).getTime() + 2 * 60 * 60 * 1000).toISOString(),
          review.rating <= 3 ? "contact_guest" : "none"
        );
      }
    });

    // Seed Google Business connection
    const insertToken = this.db.prepare(`
      INSERT INTO platform_tokens (hotelId, platform, accessToken, refreshToken, tokenType, expiresAt, scope, isActive, lastSyncAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    insertToken.run(
      hotelId,
      "google",
      "mock_access_token",
      "mock_refresh_token", 
      "Bearer",
      new Date(Date.now() + 60 * 60 * 1000).toISOString(),
      "https://www.googleapis.com/auth/business.manage",
      true,
      new Date(Date.now() - 5 * 60 * 1000).toISOString()
    );

    // Seed Google Business location
    const insertLocation = this.db.prepare(`
      INSERT INTO google_business_locations (hotelId, locationId, accountId, name, address, phoneNumber, website, averageRating, totalReviewCount, isVerified, status, lastSyncAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    insertLocation.run(
      hotelId,
      "ChIJN1t_tDeuEmsRUsoyG83frY4",
      "accounts/12345",
      "Grand Palace Hotel",
      "123 Luxury Avenue, New York, NY 10001",
      "******-0123",
      "https://grandpalacehotel.com",
      "4.2",
      3,
      true,
      "active",
      new Date(Date.now() - 5 * 60 * 1000).toISOString()
    );
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
    const user = stmt.get(id) as User | undefined;
    return user ? this.parseUser(user) : undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
    const user = stmt.get(username) as User | undefined;
    return user ? this.parseUser(user) : undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const stmt = this.db.prepare('SELECT * FROM users WHERE email = ?');
    const user = stmt.get(email) as User | undefined;
    return user ? this.parseUser(user) : undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const stmt = this.db.prepare(`
      INSERT INTO users (username, password, email, firstName, lastName, role, hotelId)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      insertUser.username,
      insertUser.password,
      insertUser.email,
      insertUser.firstName,
      insertUser.lastName,
      insertUser.role || "staff",
      insertUser.hotelId || null
    );
    
    return this.getUser(result.lastInsertRowid as number) as Promise<User>;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`).join(', ');
    const values = Object.entries(updates).filter(([key]) => key !== 'id').map(([, value]) => value);
    
    const stmt = this.db.prepare(`UPDATE users SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`);
    stmt.run(...values, id);
    
    return this.getUser(id);
  }

  // Hotel methods
  async getHotel(id: number): Promise<Hotel | undefined> {
    const stmt = this.db.prepare('SELECT * FROM hotels WHERE id = ?');
    const hotel = stmt.get(id) as Hotel | undefined;
    return hotel ? this.parseHotel(hotel) : undefined;
  }

  async createHotel(insertHotel: InsertHotel): Promise<Hotel> {
    const stmt = this.db.prepare(`
      INSERT INTO hotels (name, address, city, country, phoneNumber, email, website, description)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      insertHotel.name,
      insertHotel.address || null,
      insertHotel.city || null,
      insertHotel.country || null,
      insertHotel.phoneNumber || null,
      insertHotel.email || null,
      insertHotel.website || null,
      insertHotel.description || null
    );
    
    return this.getHotel(result.lastInsertRowid as number) as Promise<Hotel>;
  }

  async updateHotel(id: number, updates: Partial<Hotel>): Promise<Hotel | undefined> {
    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`).join(', ');
    const values = Object.entries(updates).filter(([key]) => key !== 'id').map(([, value]) => value);
    
    const stmt = this.db.prepare(`UPDATE hotels SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`);
    stmt.run(...values, id);
    
    return this.getHotel(id);
  }

  // Review methods
  async getReview(id: number): Promise<Review | undefined> {
    const stmt = this.db.prepare('SELECT * FROM reviews WHERE id = ?');
    const review = stmt.get(id) as Review | undefined;
    return review ? this.parseReview(review) : undefined;
  }

  async getReviews(filters?: { hotelId?: number; platform?: string; rating?: number; hasReply?: boolean; needsAttention?: boolean }): Promise<Review[]> {
    let query = 'SELECT * FROM reviews WHERE 1=1';
    const params: any[] = [];
    
    if (filters?.hotelId) {
      query += ' AND hotelId = ?';
      params.push(filters.hotelId);
    }
    if (filters?.platform) {
      query += ' AND platform = ?';
      params.push(filters.platform);
    }
    if (filters?.rating) {
      query += ' AND rating = ?';
      params.push(filters.rating);
    }
    if (filters?.hasReply !== undefined) {
      query += ' AND hasReply = ?';
      params.push(filters.hasReply);
    }
    if (filters?.needsAttention !== undefined) {
      query += ' AND needsAttention = ?';
      params.push(filters.needsAttention);
    }
    
    query += ' ORDER BY reviewDate DESC';
    
    const stmt = this.db.prepare(query);
    const reviews = stmt.all(...params) as Review[];
    return reviews.map(review => this.parseReview(review));
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const stmt = this.db.prepare(`
      INSERT INTO reviews (hotelId, platform, platformReviewId, guestName, guestEmail, rating, title, content, reviewDate, roomNumber, stayDuration, isVerifiedStay, metadata, hasReply, needsAttention, priority)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      insertReview.hotelId,
      insertReview.platform,
      insertReview.platformReviewId,
      insertReview.guestName,
      insertReview.guestEmail || null,
      insertReview.rating,
      insertReview.title || null,
      insertReview.content,
      this.dateToISOString(insertReview.reviewDate),
      insertReview.roomNumber || null,
      insertReview.stayDuration || null,
      insertReview.isVerifiedStay || null,
      insertReview.metadata ? JSON.stringify(insertReview.metadata) : null,
      insertReview.hasReply || false,
      insertReview.needsAttention || false,
      insertReview.priority || null
    );
    
    return this.getReview(result.lastInsertRowid as number) as Promise<Review>;
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review | undefined> {
    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`).join(', ');
    const values = Object.entries(updates).filter(([key, value]) => key !== 'id').map(([key, value]) => {
      if (key === 'reviewDate' && value instanceof Date) return this.dateToISOString(value);
      if (key === 'metadata' && value) return JSON.stringify(value);
      return value;
    });

    const stmt = this.db.prepare(`UPDATE reviews SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`);
    stmt.run(...values, id);

    return this.getReview(id);
  }

  async getReviewByPlatformId(platform: string, platformReviewId: string): Promise<Review | undefined> {
    const stmt = this.db.prepare('SELECT * FROM reviews WHERE platform = ? AND platformReviewId = ?');
    const review = stmt.get(platform, platformReviewId) as Review | undefined;
    return review ? this.parseReview(review) : undefined;
  }

  // Reply methods
  async getReply(id: number): Promise<Reply | undefined> {
    const stmt = this.db.prepare('SELECT * FROM replies WHERE id = ?');
    const reply = stmt.get(id) as Reply | undefined;
    return reply ? this.parseReply(reply) : undefined;
  }

  async getRepliesByReviewId(reviewId: number): Promise<Reply[]> {
    const stmt = this.db.prepare('SELECT * FROM replies WHERE reviewId = ? ORDER BY createdAt ASC');
    const replies = stmt.all(reviewId) as Reply[];
    return replies.map(reply => this.parseReply(reply));
  }

  async createReply(insertReply: InsertReply): Promise<Reply> {
    const stmt = this.db.prepare(`
      INSERT INTO replies (reviewId, userId, content, followUpAction, isPublished, publishedAt, platformReplyId)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      insertReply.reviewId,
      insertReply.userId,
      insertReply.content,
      insertReply.followUpAction || null,
      insertReply.isPublished || false,
      this.dateToISOString(insertReply.publishedAt),
      insertReply.platformReplyId || null
    );

    // Update the review to mark it as replied
    const updateReviewStmt = this.db.prepare(`
      UPDATE reviews SET hasReply = true, needsAttention = false, updatedAt = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    updateReviewStmt.run(insertReply.reviewId);
    
    return this.getReply(result.lastInsertRowid as number) as Promise<Reply>;
  }

  async updateReply(id: number, updates: Partial<Reply>): Promise<Reply | undefined> {
    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`).join(', ');
    const values = Object.entries(updates).filter(([key, value]) => key !== 'id').map(([key, value]) => {
      if (key === 'publishedAt' && value instanceof Date) return this.dateToISOString(value);
      return value;
    });

    const stmt = this.db.prepare(`UPDATE replies SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`);
    stmt.run(...values, id);

    return this.getReply(id);
  }

  // Platform token methods
  async getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined> {
    const stmt = this.db.prepare('SELECT * FROM platform_tokens WHERE hotelId = ? AND platform = ? AND isActive = true');
    const token = stmt.get(hotelId, platform) as PlatformToken | undefined;
    return token ? this.parsePlatformToken(token) : undefined;
  }

  async createPlatformToken(insertToken: InsertPlatformToken): Promise<PlatformToken> {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO platform_tokens (hotelId, platform, accessToken, refreshToken, tokenType, expiresAt, scope, isActive, lastSyncAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      insertToken.hotelId,
      insertToken.platform,
      insertToken.accessToken || null,
      insertToken.refreshToken || null,
      insertToken.tokenType || null,
      this.dateToISOString(insertToken.expiresAt),
      insertToken.scope || null,
      insertToken.isActive || true,
      this.dateToISOString(insertToken.lastSyncAt)
    );
    
    return this.getPlatformToken(insertToken.hotelId, insertToken.platform) as Promise<PlatformToken>;
  }

  async updatePlatformToken(id: number, updates: Partial<PlatformToken>): Promise<PlatformToken | undefined> {
    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`).join(', ');
    const values = Object.entries(updates).filter(([key]) => key !== 'id').map(([, value]) => {
      if ((key === 'expiresAt' || key === 'lastSyncAt') && value instanceof Date) return value.toISOString();
      return value;
    });
    
    const stmt = this.db.prepare(`UPDATE platform_tokens SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`);
    stmt.run(...values, id);
    
    const tokenStmt = this.db.prepare('SELECT * FROM platform_tokens WHERE id = ?');
    const token = tokenStmt.get(id) as PlatformToken | undefined;
    return token ? this.parsePlatformToken(token) : undefined;
  }

  // Google Business location methods
  async getGoogleBusinessLocations(hotelId: number): Promise<GoogleBusinessLocation[]> {
    const stmt = this.db.prepare('SELECT * FROM google_business_locations WHERE hotelId = ?');
    const locations = stmt.all(hotelId) as GoogleBusinessLocation[];
    return locations.map(location => this.parseGoogleBusinessLocation(location));
  }

  async createGoogleBusinessLocation(insertLocation: InsertGoogleBusinessLocation): Promise<GoogleBusinessLocation> {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO google_business_locations (hotelId, locationId, accountId, name, address, phoneNumber, website, averageRating, totalReviewCount, isVerified, status, lastSyncAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      insertLocation.hotelId,
      insertLocation.locationId,
      insertLocation.accountId,
      insertLocation.name,
      insertLocation.address || null,
      insertLocation.phoneNumber || null,
      insertLocation.website || null,
      insertLocation.averageRating || null,
      insertLocation.totalReviewCount || null,
      insertLocation.isVerified || null,
      insertLocation.status || null,
      this.dateToISOString(insertLocation.lastSyncAt)
    );
    
    const getStmt = this.db.prepare('SELECT * FROM google_business_locations WHERE hotelId = ? AND locationId = ?');
    const location = getStmt.get(insertLocation.hotelId, insertLocation.locationId) as GoogleBusinessLocation;
    return this.parseGoogleBusinessLocation(location);
  }

  async updateGoogleBusinessLocation(id: number, updates: Partial<GoogleBusinessLocation>): Promise<GoogleBusinessLocation | undefined> {
    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`).join(', ');
    const values = Object.entries(updates).filter(([key]) => key !== 'id').map(([, value]) => {
      if (key === 'lastSyncAt' && value instanceof Date) return value.toISOString();
      return value;
    });
    
    const stmt = this.db.prepare(`UPDATE google_business_locations SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`);
    stmt.run(...values, id);
    
    const getStmt = this.db.prepare('SELECT * FROM google_business_locations WHERE id = ?');
    const location = getStmt.get(id) as GoogleBusinessLocation | undefined;
    return location ? this.parseGoogleBusinessLocation(location) : undefined;
  }

  // Sync status methods
  async getSyncStatus(hotelId: number, platform: string): Promise<SyncStatus | undefined> {
    const stmt = this.db.prepare('SELECT * FROM sync_status WHERE hotelId = ? AND platform = ?');
    const status = stmt.get(hotelId, platform) as SyncStatus | undefined;
    return status ? this.parseSyncStatus(status) : undefined;
  }

  async updateSyncStatus(hotelId: number, platform: string, statusUpdates: Partial<SyncStatus>): Promise<SyncStatus> {
    const existing = await this.getSyncStatus(hotelId, platform);
    
    if (!existing) {
      const stmt = this.db.prepare(`
        INSERT INTO sync_status (hotelId, platform, lastSyncAt, nextSyncAt, syncFrequency, isActive, lastError, successCount, errorCount)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        hotelId,
        platform,
        this.dateToISOString(statusUpdates.lastSyncAt),
        this.dateToISOString(statusUpdates.nextSyncAt),
        statusUpdates.syncFrequency || 30,
        statusUpdates.isActive !== undefined ? statusUpdates.isActive : true,
        statusUpdates.lastError || null,
        statusUpdates.successCount || 0,
        statusUpdates.errorCount || 0
      );
    } else {
      const fields = Object.keys(statusUpdates).map(key => `${key} = ?`).join(', ');
      const values = Object.entries(statusUpdates).map(([key, value]) => {
        if ((key === 'lastSyncAt' || key === 'nextSyncAt') && value instanceof Date) return this.dateToISOString(value);
        return value;
      });

      const stmt = this.db.prepare(`UPDATE sync_status SET ${fields}, updatedAt = CURRENT_TIMESTAMP WHERE hotelId = ? AND platform = ?`);
      stmt.run(...values, hotelId, platform);
    }
    
    return this.getSyncStatus(hotelId, platform) as Promise<SyncStatus>;
  }

  // Statistics methods
  async getReviewStats(hotelId: number): Promise<ReviewStats> {
    const reviewsStmt = this.db.prepare('SELECT COUNT(*) as count FROM reviews WHERE hotelId = ?');
    const ratingStmt = this.db.prepare('SELECT AVG(rating) as avg FROM reviews WHERE hotelId = ?');
    const pendingStmt = this.db.prepare('SELECT COUNT(*) as count FROM reviews WHERE hotelId = ? AND hasReply = false');
    const responseRateStmt = this.db.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN hasReply THEN 1 ELSE 0 END) as replied
      FROM reviews WHERE hotelId = ?
    `);
    const distributionStmt = this.db.prepare(`
      SELECT rating, COUNT(*) as count 
      FROM reviews WHERE hotelId = ? 
      GROUP BY rating 
      ORDER BY rating DESC
    `);
    
    const totalResult = reviewsStmt.get(hotelId) as { count: number };
    const avgResult = ratingStmt.get(hotelId) as { avg: number };
    const pendingResult = pendingStmt.get(hotelId) as { count: number };
    const responseResult = responseRateStmt.get(hotelId) as { total: number; replied: number };
    const distributionResults = distributionStmt.all(hotelId) as Array<{ rating: number; count: number }>;
    
    const totalReviews = totalResult.count;
    const responseRate = totalReviews > 0 ? Math.round((responseResult.replied / totalReviews) * 100) : 0;
    
    const ratingDistribution: Record<string, number> = {};
    distributionResults.forEach(row => {
      ratingDistribution[row.rating.toString()] = row.count;
    });
    
    // Mock monthly trend data
    const monthlyTrend = [
      { month: 'Jan', count: 15, rating: 4.2 },
      { month: 'Feb', count: 18, rating: 4.1 },
      { month: 'Mar', count: 22, rating: 4.3 },
      { month: 'Apr', count: 19, rating: 4.0 },
      { month: 'May', count: 25, rating: 4.4 },
      { month: 'Jun', count: totalReviews, rating: avgResult.avg || 0 },
    ];
    
    return {
      totalReviews,
      averageRating: avgResult.avg || 0,
      pendingReplies: pendingResult.count,
      responseRate,
      ratingDistribution,
      monthlyTrend,
    };
  }

  async getPlatformConnections(hotelId: number): Promise<PlatformConnection[]> {
    const connections: PlatformConnection[] = [
      {
        platform: "google",
        connected: false,
        reviewCount: 0,
        lastSync: null,
        status: "inactive"
      },
      {
        platform: "tripadvisor", 
        connected: false,
        reviewCount: 0,
        lastSync: null,
        status: "inactive"
      },
      {
        platform: "booking",
        connected: false,
        reviewCount: 0,
        lastSync: null,
        status: "inactive"
      }
    ];

    // Check Google connection
    const googleTokenStmt = this.db.prepare('SELECT * FROM platform_tokens WHERE hotelId = ? AND platform = ? AND isActive = true');
    const googleToken = googleTokenStmt.get(hotelId, "google");
    
    if (googleToken) {
      const reviewCountStmt = this.db.prepare('SELECT COUNT(*) as count FROM reviews WHERE hotelId = ? AND platform = ?');
      const reviewCount = reviewCountStmt.get(hotelId, "google") as { count: number };
      
      connections[0] = {
        platform: "google",
        connected: true,
        reviewCount: reviewCount.count,
        lastSync: googleToken.lastSyncAt,
        status: "active"
      };
    }

    return connections;
  }

  // Helper method to safely convert Date objects to ISO strings for SQLite
  private dateToISOString(date: Date | null | undefined): string | null {
    if (!date) return null;
    if (!(date instanceof Date)) return null;
    try {
      return date.toISOString();
    } catch (error) {
      console.error('Error converting date to ISO string:', error);
      return null;
    }
  }

  // Validate database connection and handle potential errors
  private validateDatabaseOperation<T>(operation: () => T, operationName: string): T {
    try {
      return operation();
    } catch (error) {
      console.error(`Database operation failed (${operationName}):`, error);
      throw new Error(`Database operation failed: ${operationName}`);
    }
  }

  // Helper methods to parse database rows to proper types
  private parseUser(user: any): User {
    return {
      ...user,
      createdAt: new Date(user.createdAt),
      updatedAt: new Date(user.updatedAt),
    };
  }

  private parseHotel(hotel: any): Hotel {
    return {
      ...hotel,
      createdAt: new Date(hotel.createdAt),
      updatedAt: new Date(hotel.updatedAt),
    };
  }

  private parseReview(review: any): Review {
    return {
      ...review,
      reviewDate: new Date(review.reviewDate),
      metadata: review.metadata ? JSON.parse(review.metadata) : null,
      hasReply: Boolean(review.hasReply),
      needsAttention: Boolean(review.needsAttention),
      isVerifiedStay: review.isVerifiedStay !== null ? Boolean(review.isVerifiedStay) : null,
      createdAt: new Date(review.createdAt),
      updatedAt: new Date(review.updatedAt),
    };
  }

  private parseReply(reply: any): Reply {
    return {
      ...reply,
      isPublished: Boolean(reply.isPublished),
      publishedAt: reply.publishedAt ? new Date(reply.publishedAt) : null,
      createdAt: new Date(reply.createdAt),
      updatedAt: new Date(reply.updatedAt),
    };
  }

  private parsePlatformToken(token: any): PlatformToken {
    return {
      ...token,
      expiresAt: token.expiresAt ? new Date(token.expiresAt) : null,
      lastSyncAt: token.lastSyncAt ? new Date(token.lastSyncAt) : null,
      isActive: Boolean(token.isActive),
      createdAt: new Date(token.createdAt),
      updatedAt: new Date(token.updatedAt),
    };
  }

  private parseGoogleBusinessLocation(location: any): GoogleBusinessLocation {
    return {
      ...location,
      totalReviewCount: location.totalReviewCount || 0,
      isVerified: location.isVerified !== null ? Boolean(location.isVerified) : null,
      lastSyncAt: location.lastSyncAt ? new Date(location.lastSyncAt) : null,
      createdAt: new Date(location.createdAt),
      updatedAt: new Date(location.updatedAt),
    };
  }

  private parseSyncStatus(status: any): SyncStatus {
    return {
      ...status,
      lastSyncAt: status.lastSyncAt ? new Date(status.lastSyncAt) : null,
      nextSyncAt: status.nextSyncAt ? new Date(status.nextSyncAt) : null,
      isActive: Boolean(status.isActive),
      createdAt: new Date(status.createdAt),
      updatedAt: new Date(status.updatedAt),
    };
  }

  // Export reviews to text file
  async exportReviewsToFile(hotelId: number, filePath: string = './reviews_export.txt'): Promise<string> {
    const reviews = await this.getReviews({ hotelId });
    const hotel = await this.getHotel(hotelId);
    
    let content = `HOTEL REVIEWS EXPORT\n`;
    content += `Hotel: ${hotel?.name || 'Unknown'}\n`;
    content += `Export Date: ${new Date().toISOString()}\n`;
    content += `Total Reviews: ${reviews.length}\n`;
    content += `${'='.repeat(80)}\n\n`;
    
    for (const review of reviews) {
      const replies = await this.getRepliesByReviewId(review.id);
      
      content += `Review ID: ${review.id}\n`;
      content += `Platform: ${review.platform.toUpperCase()}\n`;
      content += `Guest: ${review.guestName}\n`;
      content += `Rating: ${'★'.repeat(review.rating)}${'☆'.repeat(5 - review.rating)} (${review.rating}/5)\n`;
      content += `Date: ${review.reviewDate.toLocaleString()}\n`;
      if (review.roomNumber) content += `Room: ${review.roomNumber}\n`;
      if (review.stayDuration) content += `Stay Duration: ${review.stayDuration}\n`;
      content += `Review: ${review.content}\n`;
      
      if (replies.length > 0) {
        content += `\nReplies:\n`;
        replies.forEach((reply, index) => {
          content += `  Reply ${index + 1}: ${reply.content}\n`;
          if (reply.publishedAt) content += `  Published: ${reply.publishedAt.toLocaleString()}\n`;
        });
      } else {
        content += `\nStatus: No reply yet\n`;
      }
      
      content += `${'-'.repeat(80)}\n\n`;
    }
    
    fs.writeFileSync(filePath, content, 'utf8');
    return filePath;
  }

  close(): void {
    this.db.close();
  }
}