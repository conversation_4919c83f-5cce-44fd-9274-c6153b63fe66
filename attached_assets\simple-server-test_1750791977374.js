// Simple server test to check if basic Express works
const express = require('express');
const app = express();

app.get('/', (req, res) => {
  res.send('✅ Server is working!');
});

app.get('/test', (req, res) => {
  res.json({ 
    status: 'Server running',
    timestamp: new Date().toISOString(),
    env: {
      GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Missing',
      GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Missing'
    }
  });
});

const port = 3001; // Use different port to avoid conflicts
app.listen(port, () => {
  console.log(`✅ Simple test server running on http://localhost:${port}`);
  console.log(`Test URL: http://localhost:${port}/test`);
});
