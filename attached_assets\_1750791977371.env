# Application Configuration
# Using SQLite database (database.sqlite file will be created automatically)

# Session secret for authentication
SESSION_SECRET="hotel-review-manager-secret-key-change-in-production"

# Google Business Profile API Configuration
# Get these from Google Cloud Console after setting up OAuth 2.0 credentials
GOOGLE_CLIENT_ID="471900131350-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GOOGLE_REDIRECT_URI="http://localhost:5000/api/auth/google/callback"

# Google API Configuration
GOOGLE_API_SCOPES="https://www.googleapis.com/auth/business.manage"
