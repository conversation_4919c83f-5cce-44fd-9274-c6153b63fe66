import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Star, User, Reply } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import type { Review, Reply as ReplyType } from "@shared/schema";

interface ReviewCardProps {
  review: Review;
  replies?: ReplyType[];
  onReply?: (reviewId: number) => void;
}

export function ReviewCard({ review, replies, onReply }: ReviewCardProps) {
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-4 h-4 ${
            i <= rating ? "text-yellow-400 fill-current" : "text-gray-300 dark:text-gray-600"
          }`}
        />
      );
    }
    return stars;
  };

  const getPlatformBadge = (platform: string) => {
    switch (platform) {
      case "google":
        return (
          <Badge className="platform-badge google">
            Google Business
          </Badge>
        );
      case "tripadvisor":
        return (
          <Badge className="platform-badge tripadvisor">
            TripAdvisor
          </Badge>
        );
      case "booking":
        return (
          <Badge className="platform-badge booking">
            Booking.com
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            {platform}
          </Badge>
        );
    }
  };

  const getStatusIndicator = () => {
    if (review.hasReply) {
      return (
        <div className="flex items-center space-x-2">
          <div className="status-indicator bg-blue-500" />
          <span className="text-xs text-blue-700 dark:text-blue-300">Replied</span>
        </div>
      );
    }

    if (review.needsAttention) {
      return (
        <div className="flex items-center space-x-2">
          <div className="status-indicator bg-amber-500 animate-pulse" />
          <span className="text-xs text-amber-700 dark:text-amber-300 font-medium">
            Needs Reply
          </span>
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-2">
        <div className="status-indicator bg-green-500" />
        <span className="text-xs text-green-700 dark:text-green-300">No reply needed</span>
      </div>
    );
  };

  const latestReply = replies && replies.length > 0 ? replies[replies.length - 1] : null;

  return (
    <Card 
      className={`review-card ${
        review.needsAttention ? "border-l-4 border-l-amber-500" : ""
      }`}
    >
      <CardContent className="p-6">
        <div className="flex space-x-4">
          <Avatar className="w-12 h-12">
            <AvatarFallback>
              <User className="w-6 h-6" />
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className="font-medium text-foreground">
                    {review.guestName}
                  </h4>
                  <div className="flex space-x-1">
                    {renderStars(review.rating)}
                  </div>
                  {getPlatformBadge(review.platform)}
                  {review.priority !== "normal" && (
                    <Badge 
                      variant="destructive" 
                      className={`priority-badge ${review.priority}`}
                    >
                      {review.priority}
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm text-muted-foreground mb-3">
                  {review.content}
                </p>
                
                <div className="flex items-center space-x-4 mb-3">
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(review.reviewDate), { addSuffix: true })}
                  </span>
                  {review.roomNumber && (
                    <span className="text-xs text-muted-foreground">
                      Room {review.roomNumber}
                    </span>
                  )}
                  {review.stayDuration && (
                    <span className="text-xs text-muted-foreground">
                      {review.stayDuration}
                    </span>
                  )}
                </div>

                {/* Reply Section */}
                {latestReply && latestReply.isPublished && (
                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                    <div className="flex items-start space-x-2">
                      <div className="w-6 h-6 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mt-0.5">
                        <Reply className="w-3 h-3 text-blue-600 dark:text-blue-300" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-foreground">
                          Hotel Response
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {latestReply.content}
                        </p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className="text-xs text-muted-foreground">
                            {latestReply.publishedAt && 
                              formatDistanceToNow(new Date(latestReply.publishedAt), { addSuffix: true })
                            }
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex flex-col items-end space-y-2 ml-4">
                {getStatusIndicator()}
                {!review.hasReply && onReply && (
                  <Button
                    size="sm"
                    onClick={() => onReply(review.id)}
                    className="mt-2"
                  >
                    Reply Now
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
