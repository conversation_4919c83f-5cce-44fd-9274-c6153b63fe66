@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(222, 84%, 4.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(222, 84%, 4.9%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(222, 84%, 4.9%);
  --primary: hsl(222, 47%, 11%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(222, 84%, 4.9%);
  --muted: hsl(210, 40%, 98%);
  --muted-foreground: hsl(215, 16%, 47%);
  --accent: hsl(217, 91%, 60%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --ring: hsl(217, 91%, 60%);
  --radius: 0.5rem;
  --chart-1: hsl(12, 76%, 61%);
  --chart-2: hsl(173, 58%, 39%);
  --chart-3: hsl(197, 37%, 24%);
  --chart-4: hsl(43, 74%, 66%);
  --chart-5: hsl(27, 87%, 67%);
}

.dark {
  --background: hsl(222, 84%, 4.9%);
  --foreground: hsl(210, 40%, 98%);
  --card: hsl(222, 84%, 4.9%);
  --card-foreground: hsl(210, 40%, 98%);
  --popover: hsl(222, 84%, 4.9%);
  --popover-foreground: hsl(210, 40%, 98%);
  --primary: hsl(210, 40%, 98%);
  --primary-foreground: hsl(222, 47%, 11%);
  --secondary: hsl(217, 32%, 17%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --muted: hsl(217, 32%, 17%);
  --muted-foreground: hsl(215, 20%, 65%);
  --accent: hsl(217, 91%, 60%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62%, 30%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --border: hsl(217, 32%, 17%);
  --input: hsl(217, 32%, 17%);
  --ring: hsl(217, 91%, 60%);
  --chart-1: hsl(220, 70%, 50%);
  --chart-2: hsl(160, 60%, 45%);
  --chart-3: hsl(30, 80%, 55%);
  --chart-4: hsl(280, 65%, 60%);
  --chart-5: hsl(340, 75%, 55%);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

@layer components {
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
  }
  
  .sidebar-transition {
    transition: transform 0.3s ease-in-out;
  }
  
  .star-rating {
    color: hsl(45, 93%, 47%);
  }
  
  .stat-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm transition-all hover:shadow-md;
  }
  
  .review-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-all;
  }
  
  .platform-badge {
    @apply px-2 py-1 text-xs font-medium rounded-full;
  }
  
  .platform-badge.google {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }
  
  .platform-badge.tripadvisor {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }
  
  .platform-badge.booking {
    @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
  }
  
  .priority-badge.urgent {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }
  
  .priority-badge.high {
    @apply bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200;
  }
  
  .priority-badge.normal {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
  }
  
  .status-indicator {
    @apply w-2 h-2 rounded-full;
  }
  
  .status-indicator.active {
    @apply bg-green-500 animate-pulse;
  }
  
  .status-indicator.inactive {
    @apply bg-gray-400;
  }
  
  .status-indicator.error {
    @apply bg-red-500;
  }
}

@media (max-width: 1024px) {
  .sidebar-hidden {
    transform: translateX(-100%);
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Animation for new reviews */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

.pulse-ring {
  @apply absolute inline-flex rounded-full opacity-75 animate-ping;
}
