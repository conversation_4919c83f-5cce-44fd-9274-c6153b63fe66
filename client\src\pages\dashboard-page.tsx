import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { TopBar } from "@/components/TopBar";
import { StatsGrid } from "@/components/StatsGrid";
import { PlatformStatus } from "@/components/PlatformStatus";
import { RecentActivity } from "@/components/RecentActivity";
import { ReviewsTable } from "@/components/ReviewsTable";

export default function DashboardPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // TODO: Implement search functionality
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          title="Dashboard Overview"
          subtitle="Monitor your hotel's online reputation"
          onSearch={handleSearch}
        />

        {/* Dashboard Content */}
        <main className="flex-1 overflow-auto p-6 custom-scrollbar">
          {/* Stats Grid */}
          <StatsGrid />

          {/* Platform Status and Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <PlatformStatus />
            <RecentActivity />
          </div>

          {/* Reviews Table */}
          <ReviewsTable />
        </main>
      </div>
    </div>
  );
}
