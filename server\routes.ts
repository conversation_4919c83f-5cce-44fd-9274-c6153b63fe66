import type { Express } from "express";
import { createServer, type Server } from "http";
import express from "express";
import session from "express-session";
import MemoryStore from "memorystore";
import passport from "./auth";
import { requireAuth } from "./auth";
import { storage } from "./storage";
import { googleBusinessAPI } from "./services/google-business-api";
import { loginSchema, replySchema } from "@shared/schema";
import { z } from "zod";

const MemoryStoreSession = MemoryStore(session);

export async function registerRoutes(app: Express): Promise<Server> {
  // Session configuration
  app.use(session({
    secret: process.env.SESSION_SECRET || 'hotel-review-manager-secret',
    resave: false,
    saveUninitialized: false,
    store: new MemoryStoreSession({
      checkPeriod: 86400000 // 24 hours
    }),
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
  }));

  // Passport middleware
  app.use(passport.initialize());
  app.use(passport.session());

  // Auth routes
  app.post('/api/login', (req, res, next) => {
    try {
      const { username, password } = loginSchema.parse(req.body);
      
      passport.authenticate('local', (err: any, user: any, info: any) => {
        if (err) {
          return res.status(500).json({ message: 'Internal server error' });
        }
        
        if (!user) {
          return res.status(401).json({ message: info?.message || 'Authentication failed' });
        }
        
        req.logIn(user, (loginErr) => {
          if (loginErr) {
            return res.status(500).json({ message: 'Login failed' });
          }
          
          const { password, ...userWithoutPassword } = user;
          res.json({ user: userWithoutPassword });
        });
      })(req, res, next);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: 'Validation error', 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/logout', (req, res) => {
    req.logout((err) => {
      if (err) {
        return res.status(500).json({ message: 'Logout failed' });
      }
      res.json({ message: 'Logged out successfully' });
    });
  });

  app.get('/api/user', (req, res) => {
    if (req.isAuthenticated()) {
      const { password, ...userWithoutPassword } = req.user as any;
      res.json(userWithoutPassword);
    } else {
      res.status(401).json({ message: 'Not authenticated' });
    }
  });

  // Reviews routes
  app.get('/api/reviews', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const { platform, rating, hasReply, needsAttention } = req.query;
      
      const filters: any = { hotelId: user.hotelId };
      
      if (platform) filters.platform = platform;
      if (rating) filters.rating = parseInt(rating as string);
      if (hasReply !== undefined) filters.hasReply = hasReply === 'true';
      if (needsAttention !== undefined) filters.needsAttention = needsAttention === 'true';
      
      const reviews = await storage.getReviews(filters);
      res.json(reviews);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      res.status(500).json({ message: 'Failed to fetch reviews' });
    }
  });

  app.get('/api/reviews/:id', requireAuth, async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const review = await storage.getReview(reviewId);
      
      if (!review) {
        return res.status(404).json({ message: 'Review not found' });
      }
      
      const user = req.user as any;
      if (review.hotelId !== user.hotelId) {
        return res.status(403).json({ message: 'Access denied' });
      }
      
      res.json(review);
    } catch (error) {
      console.error('Error fetching review:', error);
      res.status(500).json({ message: 'Failed to fetch review' });
    }
  });

  app.post('/api/reviews/:id/reply', requireAuth, async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const { content, followUpAction } = replySchema.parse(req.body);
      const user = req.user as any;
      
      const review = await storage.getReview(reviewId);
      if (!review) {
        return res.status(404).json({ message: 'Review not found' });
      }
      
      if (review.hotelId !== user.hotelId) {
        return res.status(403).json({ message: 'Access denied' });
      }
      
      // Create reply in database
      const reply = await storage.createReply({
        reviewId,
        userId: user.id,
        content,
        isPublished: false,
        publishedAt: null,
        platformReplyId: null,
        followUpAction: followUpAction || "none",
      });

      // Post reply to platform if it's Google
      if (review.platform === "google") {
        try {
          await googleBusinessAPI.postReply(reviewId, content);
          
          // Update reply as published
          await storage.updateReply(reply.id, {
            isPublished: true,
            publishedAt: new Date(),
            platformReplyId: `google_reply_${Date.now()}`,
          });
        } catch (platformError) {
          console.error('Failed to post reply to platform:', platformError);
          // Reply saved locally but not posted to platform
        }
      }
      
      res.json(reply);
    } catch (error) {
      console.error('Error creating reply:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: 'Validation error', 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: 'Failed to create reply' });
    }
  });

  app.get('/api/reviews/:id/replies', requireAuth, async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const replies = await storage.getRepliesByReviewId(reviewId);
      res.json(replies);
    } catch (error) {
      console.error('Error fetching replies:', error);
      res.status(500).json({ message: 'Failed to fetch replies' });
    }
  });

  // Statistics routes
  app.get('/api/statistics', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const stats = await storage.getReviewStats(user.hotelId);
      res.json(stats);
    } catch (error) {
      console.error('Error fetching statistics:', error);
      res.status(500).json({ message: 'Failed to fetch statistics' });
    }
  });

  // Platform routes
  app.get('/api/platforms/connected', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const connections = await storage.getPlatformConnections(user.hotelId);
      res.json(connections);
    } catch (error) {
      console.error('Error fetching platform connections:', error);
      res.status(500).json({ message: 'Failed to fetch platform connections' });
    }
  });

  // Import automation service
  const { reviewAutomationService } = await import('./services/review-automation');

  // Google Business Profile routes
  app.get('/api/google/test', async (req, res) => {
    try {
      const result = await googleBusinessAPI.testConfiguration();
      res.json(result);
    } catch (error) {
      console.error('Error testing Google API:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Failed to test Google API configuration' 
      });
    }
  });

  app.get('/api/auth/google', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const authUrl = googleBusinessAPI.generateAuthUrl(user.hotelId);
      res.json({ authUrl });
    } catch (error) {
      console.error('Error generating Google auth URL:', error);
      res.status(500).json({ message: 'Failed to generate authentication URL' });
    }
  });

  app.get('/api/auth/google/callback', async (req, res) => {
    try {
      const { code, state } = req.query;
      
      if (!code || !state) {
        return res.status(400).json({ message: 'Missing authorization code or state' });
      }
      
      const hotelId = parseInt(state as string);
      await googleBusinessAPI.exchangeCodeForTokens(code as string, hotelId);
      
      // Redirect back to the platforms page
      res.redirect('/platforms?connected=google');
    } catch (error) {
      console.error('Error handling Google callback:', error);
      res.redirect('/platforms?error=google_auth_failed');
    }
  });

  app.get('/api/google/accounts', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const accounts = await googleBusinessAPI.getBusinessAccounts(user.hotelId);
      res.json(accounts);
    } catch (error) {
      console.error('Error fetching Google accounts:', error);
      res.status(500).json({ message: 'Failed to fetch Google Business accounts' });
    }
  });

  app.get('/api/google/locations', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const { accountName } = req.query;
      
      if (!accountName) {
        return res.status(400).json({ message: 'Account name is required' });
      }
      
      const locations = await googleBusinessAPI.getBusinessLocations(
        user.hotelId, 
        accountName as string
      );
      res.json(locations);
    } catch (error) {
      console.error('Error fetching Google locations:', error);
      res.status(500).json({ message: 'Failed to fetch Google Business locations' });
    }
  });

  app.post('/api/google/sync-reviews', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const result = await googleBusinessAPI.syncReviews(user.hotelId);
      res.json(result);
    } catch (error) {
      console.error('Error syncing Google reviews:', error);
      res.status(500).json({ message: 'Failed to sync reviews from Google Business Profile' });
    }
  });

  // Hotel routes
  app.get('/api/hotel', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      const hotel = await storage.getHotel(user.hotelId);
      
      if (!hotel) {
        return res.status(404).json({ message: 'Hotel not found' });
      }
      
      res.json(hotel);
    } catch (error) {
      console.error('Error fetching hotel:', error);
      res.status(500).json({ message: 'Failed to fetch hotel information' });
    }
  });

  // Activity feed (recent actions)
  app.get('/api/activity', requireAuth, async (req, res) => {
    try {
      const user = req.user as any;
      
      // Get recent reviews and replies for activity feed
      const recentReviews = await storage.getReviews({ hotelId: user.hotelId });
      const activities: Array<{
        id: string;
        type: string;
        title: string;
        description: string;
        platform: string;
        guestName: string;
        timestamp: Date;
        needsAttention: boolean | null;
      }> = [];
      
      // Add recent reviews
      recentReviews.slice(0, 5).forEach(review => {
        activities.push({
          id: `review_${review.id}`,
          type: 'review',
          title: `New ${review.rating}-star review received`,
          description: review.content.substring(0, 100) + '...',
          platform: review.platform,
          guestName: review.guestName,
          timestamp: review.reviewDate,
          needsAttention: review.needsAttention,
        });
      });

      // Sort by timestamp
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      res.json(activities.slice(0, 10));
    } catch (error) {
      console.error('Error fetching activity feed:', error);
      res.status(500).json({ message: 'Failed to fetch activity feed' });
    }
  });

  // Review automation routes
  app.get("/api/automation/status", requireAuth, async (req, res) => {
    try {
      const { reviewAutomationService } = await import('./services/review-automation');
      const status = await reviewAutomationService.getSyncStatus();
      res.json(status);
    } catch (error) {
      console.error('Error getting automation status:', error);
      res.status(500).json({ message: 'Failed to get automation status' });
    }
  });

  app.post("/api/automation/export", requireAuth, async (req, res) => {
    try {
      const { reviewAutomationService } = await import('./services/review-automation');
      const user = req.user as any;
      const filePath = await reviewAutomationService.exportCurrentReviews(user.hotelId);
      res.json({ 
        message: 'Reviews exported successfully', 
        filePath,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error exporting reviews:', error);
      res.status(500).json({ message: 'Failed to export reviews' });
    }
  });

  app.post("/api/automation/start", requireAuth, async (req, res) => {
    try {
      const { reviewAutomationService } = await import('./services/review-automation');
      reviewAutomationService.startAutomatedSync();
      res.json({ message: 'Automated sync started successfully' });
    } catch (error) {
      console.error('Error starting automation:', error);
      res.status(500).json({ message: 'Failed to start automation' });
    }
  });

  app.post("/api/automation/stop", requireAuth, async (req, res) => {
    try {
      const { reviewAutomationService } = await import('./services/review-automation');
      reviewAutomationService.stopAutomatedSync();
      res.json({ message: 'Automated sync stopped successfully' });
    } catch (error) {
      console.error('Error stopping automation:', error);
      res.status(500).json({ message: 'Failed to stop automation' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
