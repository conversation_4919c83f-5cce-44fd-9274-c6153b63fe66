import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { TopBar } from "@/components/TopBar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { PlayCircle, StopCircle, Download, Clock, CheckCircle, AlertCircle, FileText } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

export default function AutomationPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: automationStatus, isLoading } = useQuery({
    queryKey: ["/api/automation/status"],
    queryFn: () => api.request("/api/automation/status"),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const exportMutation = useMutation({
    mutationFn: () => api.request("/api/automation/export", { method: "POST" }),
    onSuccess: (data) => {
      toast({
        title: "Export completed",
        description: `Reviews exported successfully to ${data.filePath}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Export failed",
        description: error.message || "Failed to export reviews",
        variant: "destructive",
      });
    },
  });

  const startAutomationMutation = useMutation({
    mutationFn: () => api.request("/api/automation/start", { method: "POST" }),
    onSuccess: () => {
      toast({
        title: "Automation started",
        description: "Automated review sync is now running",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/automation/status"] });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to start automation",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const stopAutomationMutation = useMutation({
    mutationFn: () => api.request("/api/automation/stop", { method: "POST" }),
    onSuccess: () => {
      toast({
        title: "Automation stopped",
        description: "Automated review sync has been stopped",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/automation/status"] });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to stop automation",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleExport = () => {
    exportMutation.mutate();
  };

  const handleStartAutomation = () => {
    startAutomationMutation.mutate();
  };

  const handleStopAutomation = () => {
    stopAutomationMutation.mutate();
  };

  const getStatusBadge = (status: any) => {
    if (!status) return <Badge variant="secondary">Inactive</Badge>;
    
    if (status.isActive) {
      return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
    } else {
      return <Badge variant="destructive">Stopped</Badge>;
    }
  };

  const getStatusIcon = (status: any) => {
    if (!status || !status.isActive) {
      return <StopCircle className="w-5 h-5 text-red-500" />;
    }
    return <PlayCircle className="w-5 h-5 text-green-500" />;
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />

      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          title="Review Automation"
          subtitle="Automated Google Business review sync and management"
        />

        <main className="flex-1 overflow-auto p-6 custom-scrollbar">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Automation Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  {getStatusIcon(automationStatus)}
                  <span>Automation Status</span>
                  {getStatusBadge(automationStatus)}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Real-time Google Business review synchronization
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Sync Frequency:</span>
                      <span className="font-medium">Every 30 minutes</span>
                    </div>
                    {automationStatus?.lastSyncAt && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Last Sync:</span>
                        <span className="font-medium">
                          {new Date(automationStatus.lastSyncAt).toLocaleString()}
                        </span>
                      </div>
                    )}
                    {automationStatus?.nextSyncAt && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Next Sync:</span>
                        <span className="font-medium">
                          {new Date(automationStatus.nextSyncAt).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Success Count:</span>
                      <span className="font-medium text-green-600">
                        {automationStatus?.successCount || 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Error Count:</span>
                      <span className="font-medium text-red-600">
                        {automationStatus?.errorCount || 0}
                      </span>
                    </div>
                    {automationStatus?.lastError && (
                      <div className="flex items-start justify-between">
                        <span className="text-sm text-muted-foreground">Last Error:</span>
                        <span className="text-xs text-red-600 max-w-48 text-right">
                          {automationStatus.lastError}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <Separator className="my-6" />

                <div className="flex flex-wrap gap-3">
                  <Button
                    onClick={handleStartAutomation}
                    disabled={startAutomationMutation.isPending || (automationStatus?.isActive)}
                    className="flex items-center space-x-2"
                  >
                    <PlayCircle className="w-4 h-4" />
                    <span>Start Automation</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={handleStopAutomation}
                    disabled={stopAutomationMutation.isPending || (!automationStatus?.isActive)}
                    className="flex items-center space-x-2"
                  >
                    <StopCircle className="w-4 h-4" />
                    <span>Stop Automation</span>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={handleExport}
                    disabled={exportMutation.isPending}
                    className="flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>{exportMutation.isPending ? "Exporting..." : "Export Reviews"}</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Features Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Automation Features</CardTitle>
                <p className="text-sm text-muted-foreground">
                  What happens during automated sync
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Review Synchronization</h4>
                        <p className="text-sm text-muted-foreground">
                          Automatically fetches new Google Business reviews every 30 minutes
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Text File Export</h4>
                        <p className="text-sm text-muted-foreground">
                          Exports all reviews to formatted text files with timestamps
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Auto-Reply to Negative Reviews</h4>
                        <p className="text-sm text-muted-foreground">
                          Automatically responds to reviews with 3 stars or less
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <Clock className="w-5 h-5 text-purple-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Real-time Processing</h4>
                        <p className="text-sm text-muted-foreground">
                          Processes and categorizes reviews based on rating and content
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Priority Assignment</h4>
                        <p className="text-sm text-muted-foreground">
                          Automatically marks low-rated reviews as needing attention
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Comprehensive Reporting</h4>
                        <p className="text-sm text-muted-foreground">
                          Generates detailed reports with statistics and analysis
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Export Information */}
            <Card>
              <CardHeader>
                <CardTitle>Export Details</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Review export format and location
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Export File Locations:</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• <code>./current_reviews.txt</code> - Latest export (always updated)</li>
                      <li>• <code>./reviews_exports/</code> - Timestamped exports directory</li>
                      <li>• <code>./reviews_exports/reviews_Hotel_Name_YYYY-MM-DD.txt</code> - Individual exports</li>
                    </ul>
                  </div>

                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Export Contents:</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Complete review details (guest name, rating, content, date)</li>
                      <li>• Hotel replies and response status</li>
                      <li>• Priority levels and attention flags</li>
                      <li>• Summary statistics and response rates</li>
                      <li>• Rating distribution breakdown</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}