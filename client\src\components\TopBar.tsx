import { Menu, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { useState } from "react";

interface TopBarProps {
  onToggleSidebar: () => void;
  title: string;
  subtitle?: string;
  onSearch?: (query: string) => void;
}

export function TopBar({ onToggleSidebar, title, subtitle, onSearch }: TopBarProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const { data: syncStatus } = useQuery({
    queryKey: ["/api/platforms/connected"],
    queryFn: api.getPlatformConnections,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearch?.(value);
  };

  const getLastSyncStatus = () => {
    if (!syncStatus) return null;
    
    const googleConnection = syncStatus.find((conn: any) => conn.platform === "google");
    if (!googleConnection?.connected) return null;

    const lastSync = googleConnection.lastSync;
    if (!lastSync) return "Never synced";

    const timeDiff = Date.now() - new Date(lastSync).getTime();
    const minutesAgo = Math.floor(timeDiff / (1000 * 60));
    
    if (minutesAgo < 1) return "Just now";
    if (minutesAgo === 1) return "1 minute ago";
    if (minutesAgo < 60) return `${minutesAgo} minutes ago`;
    
    const hoursAgo = Math.floor(minutesAgo / 60);
    if (hoursAgo === 1) return "1 hour ago";
    if (hoursAgo < 24) return `${hoursAgo} hours ago`;
    
    return "More than a day ago";
  };

  const syncStatusText = getLastSyncStatus();

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={onToggleSidebar}
          >
            <Menu className="w-5 h-5" />
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-foreground">{title}</h2>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Sync Status */}
          {syncStatusText && (
            <Badge variant="secondary" className="bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-200">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2" />
              Last sync: {syncStatusText}
            </Badge>
          )}
          
          {/* Search */}
          {onSearch && (
            <div className="relative">
              <Input
                type="text"
                placeholder="Search reviews..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-64 pl-10"
              />
              <Search className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
