import { TrendingU<PERSON>, Star, Award, MessageCircle, CheckCircle, Clock } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import type { ReviewStats } from "@shared/schema";

export function StatsGrid() {
  const { data: stats, isLoading } = useQuery<ReviewStats>({
    queryKey: ["/api/statistics"],
    queryFn: api.getStatistics,
  });

  const statCards = [
    {
      title: "Total Reviews",
      value: stats?.totalReviews?.toLocaleString() || "0",
      change: "+12% from last month",
      changeType: "positive" as const,
      icon: Star,
      iconBg: "bg-blue-50 dark:bg-blue-900",
      iconColor: "text-blue-600 dark:text-blue-300",
    },
    {
      title: "Average Rating",
      value: stats?.averageRating?.toFixed(1) || "0.0",
      change: "across all platforms",
      changeType: "neutral" as const,
      icon: Award,
      iconBg: "bg-yellow-50 dark:bg-yellow-900",
      iconColor: "text-yellow-600 dark:text-yellow-300",
      showStars: true,
    },
    {
      title: "Pending Replies",
      value: stats?.pendingReplies?.toString() || "0",
      change: "Needs attention",
      changeType: stats && stats.pendingReplies > 0 ? "warning" : "neutral",
      icon: MessageCircle,
      iconBg: "bg-amber-50 dark:bg-amber-900",
      iconColor: "text-amber-600 dark:text-amber-300",
    },
    {
      title: "Response Rate",
      value: `${stats?.responseRate || 0}%`,
      change: "+5% improvement",
      changeType: "positive" as const,
      icon: CheckCircle,
      iconBg: "bg-green-50 dark:bg-green-900",
      iconColor: "text-green-600 dark:text-green-300",
    },
  ];

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-4 h-4 ${
            i <= rating ? "text-yellow-400 fill-current" : "text-gray-300 dark:text-gray-600"
          }`}
        />
      );
    }
    return stars;
  };

  const getChangeColor = (type: "positive" | "negative" | "warning" | "neutral") => {
    switch (type) {
      case "positive":
        return "text-green-600 dark:text-green-400";
      case "negative":
        return "text-red-600 dark:text-red-400";
      case "warning":
        return "text-amber-600 dark:text-amber-400";
      default:
        return "text-muted-foreground";
    }
  };

  const getChangeIcon = (type: "positive" | "negative" | "warning" | "neutral") => {
    switch (type) {
      case "positive":
        return <TrendingUp className="w-4 h-4 mr-1" />;
      case "warning":
        return <Clock className="w-4 h-4 mr-1" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="stat-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="skeleton h-4 w-24" />
                  <div className="skeleton h-8 w-16" />
                  <div className="skeleton h-4 w-32" />
                </div>
                <div className="skeleton w-12 h-12 rounded-lg" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="stat-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-foreground">
                    {stat.value}
                  </p>
                  {stat.showStars && stats?.averageRating && (
                    <div className="flex items-center mt-1 space-x-1">
                      {renderStars(Math.round(stats.averageRating))}
                      <span className="text-sm text-muted-foreground ml-2">
                        {stat.change}
                      </span>
                    </div>
                  )}
                  {!stat.showStars && (
                    <p className={`text-sm flex items-center mt-1 ${getChangeColor(stat.changeType)}`}>
                      {getChangeIcon(stat.changeType)}
                      {stat.change}
                    </p>
                  )}
                </div>
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${stat.iconBg}`}>
                  <Icon className={`w-6 h-6 ${stat.iconColor}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
