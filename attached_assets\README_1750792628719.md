# 🏨 Hotel Review Manager

A comprehensive, real-time hotel review management system that centralizes reviews from multiple platforms (Google Business Profile, TripAdvisor, Booking.com) into a unified dashboard for hotels to monitor, respond to, and analyze guest feedback.

## 🎯 Project Overview

**Hotel Review Manager** is a modern web application designed to help hotel owners and managers efficiently handle guest reviews across multiple platforms. The system provides real-time synchronization, intelligent analytics, and streamlined response workflows to enhance guest satisfaction and online reputation management.

### 🌟 Key Features

#### ✅ **Currently Implemented**
- **Multi-Platform Review Aggregation**: Centralized dashboard for all review platforms
- **Google Business Profile Integration**: Full OAuth 2.0 authentication and API integration
- **Real-Time Review Sync**: Automated fetching and synchronization of new reviews
- **Intelligent Review Management**: Filter, search, and sort reviews by platform, rating, date, and reply status
- **Staff Reply System**: Respond to reviews directly from the dashboard
- **Analytics Dashboard**: Comprehensive statistics and performance metrics
- **User Authentication**: Secure login system with role-based access control
- **Responsive Design**: Modern UI built with React and Tailwind CSS
- **Database Management**: Robust SQLite database with Drizzle ORM

#### 🚧 **In Development**
- **Real-Time Notifications**: WebSocket-based live updates for new reviews
- **Advanced Analytics**: Sentiment analysis and trend reporting
- **Automated Response Templates**: AI-powered response suggestions
- **Performance Optimization**: Redis caching and connection pooling

#### 🔮 **Future Roadmap**
- **TripAdvisor Integration**: API access and review synchronization
- **Booking.com Integration**: Partner API integration for guest reviews
- **AI-Powered Insights**: Machine learning for review sentiment analysis
- **Mobile Application**: Native iOS and Android apps
- **Multi-Hotel Management**: Enterprise features for hotel chains
- **Advanced Reporting**: Custom reports and data export capabilities

## 🛠️ Technology Stack

### **Backend**
- **Runtime**: Node.js 20+ with TypeScript
- **Framework**: Express.js with modern middleware
- **Database**: SQLite with Drizzle ORM
- **Authentication**: Passport.js with session management
- **API Integration**: Google APIs client library
- **Validation**: Zod schema validation
- **Development**: tsx for TypeScript execution

### **Frontend**
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter (lightweight React router)
- **State Management**: TanStack Query (React Query)
- **UI Components**: Radix UI primitives
- **Styling**: Tailwind CSS with custom theme
- **Forms**: React Hook Form with validation
- **Icons**: React Icons and Lucide React
- **Charts**: Recharts for analytics visualization

### **Development & Deployment**
- **Build Tool**: Vite with React plugin
- **Package Manager**: npm
- **Database Migrations**: Drizzle Kit
- **Development Server**: Hot reload with Vite
- **Production Build**: Optimized static assets
- **Deployment**: Replit-ready configuration

## 📁 Project Structure

```
Hotel-sayed-project/
├── client/                     # Frontend React application
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   │   ├── ReviewCard.tsx  # Individual review display
│   │   │   ├── ReviewReplyForm.tsx
│   │   │   └── PlatformConnectionModal.tsx
│   │   ├── pages/              # Application pages
│   │   │   ├── home-page.tsx   # Main dashboard
│   │   │   ├── analytics-page.tsx
│   │   │   ├── platforms-page.tsx
│   │   │   └── auth-page.tsx
│   │   ├── hooks/              # Custom React hooks
│   │   │   └── use-auth.tsx    # Authentication hook
│   │   ├── lib/                # Utility libraries
│   │   │   ├── queryClient.ts  # TanStack Query setup
│   │   │   └── exportUtils.ts  # Data export utilities
│   │   └── App.tsx             # Main application component
│   └── index.html              # HTML entry point
├── server/                     # Backend Express application
│   ├── services/               # Business logic services
│   │   └── google-business-api.ts  # Google API integration
│   ├── routes.ts               # API route definitions
│   ├── storage.ts              # Database operations
│   ├── auth.ts                 # Authentication logic
│   ├── db.ts                   # Database connection
│   └── index.ts                # Server entry point
├── shared/                     # Shared TypeScript definitions
│   └── schema.ts               # Database schema and types
├── migrations/                 # Database migration files
├── .env                        # Environment variables
├── package.json                # Dependencies and scripts
├── drizzle.config.ts          # Database configuration
├── vite.config.ts             # Frontend build configuration
└── tsconfig.json              # TypeScript configuration
```

## 🚀 Getting Started

### **Prerequisites**
- Node.js 20 or higher
- npm package manager
- Google Cloud Console account (for Google Business Profile integration)

### **Installation**

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Hotel-sayed-project
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env` file with the following variables:
   ```env
   # Application Configuration
   SESSION_SECRET="your-secure-session-secret"
   
   # Google Business Profile API Configuration
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   GOOGLE_REDIRECT_URI="http://localhost:5000/api/auth/google/callback"
   GOOGLE_API_SCOPES="https://www.googleapis.com/auth/business.manage"
   ```

4. **Database Setup**
   ```bash
   npm run db:push
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

6. **Access Application**
   Open your browser to `http://localhost:5000`

### **Google Business Profile Setup**

1. **Create Google Cloud Project**
   - Visit [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project
   - Enable billing

2. **Enable Required APIs**
   - Go to "APIs & Services" > "Library"
   - Enable "My Business Account Management API"
   - Enable "My Business Business Information API"

3. **Create OAuth 2.0 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Create OAuth 2.0 Client ID
   - Add authorized redirect URI: `http://localhost:5000/api/auth/google/callback`
   - Copy Client ID and Client Secret to your `.env` file

## 📊 Database Schema

### **Core Tables**
- **`users`**: Staff authentication and role management
- **`hotels`**: Hotel property information
- **`reviews`**: Centralized review storage with platform metadata
- **`replies`**: Staff responses to reviews
- **`platform_tokens`**: OAuth tokens for API access

### **Google Integration Tables**
- **`google_business_locations`**: Google Business location mapping
- **`sync_status`**: Real-time sync tracking and scheduling

## 🔌 API Endpoints

### **Authentication**
- `POST /api/register` - User registration
- `POST /api/login` - User authentication
- `POST /api/logout` - User logout

### **Review Management**
- `GET /api/reviews` - List reviews with filtering and pagination
- `POST /api/reviews/:id/reply` - Reply to a specific review
- `GET /api/statistics` - Review analytics and statistics

### **Google Business Profile**
- `GET /api/auth/google` - Initiate Google OAuth flow
- `GET /api/auth/google/callback` - Handle OAuth callback
- `GET /api/google/accounts` - List Google Business accounts
- `GET /api/google/locations` - List business locations
- `POST /api/google/sync-reviews` - Sync reviews from Google
- `GET /api/google/test` - Test API configuration

### **Platform Management**
- `GET /api/platforms/connected` - Check platform connection status

## 🎨 User Interface

### **Dashboard Pages**
- **Home**: Review overview with quick stats and recent reviews
- **Analytics**: Comprehensive performance metrics and trends
- **Platforms**: Platform connection management and sync status
- **Notifications**: Real-time alerts and system notifications
- **Settings**: Application configuration and preferences
- **Profile**: User account management

### **Key Components**
- **ReviewCard**: Individual review display with reply functionality
- **PlatformConnectionModal**: OAuth flow for platform integration
- **Analytics Charts**: Visual representation of review data
- **Filter Controls**: Advanced review filtering and search

## 🔄 Development Workflow

### **Available Scripts**
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build production-ready application
- `npm run start` - Start production server
- `npm run check` - TypeScript type checking
- `npm run db:push` - Apply database schema changes

### **Development Features**
- **Hot Module Replacement**: Instant updates during development
- **TypeScript Support**: Full type safety across the application
- **Error Overlay**: Runtime error display in development
- **Database Migrations**: Automated schema updates

## 🚀 Deployment

### **Production Build**
```bash
npm run build
npm run start
```

### **Environment Variables for Production**
Ensure all environment variables are properly configured for your production environment, including secure session secrets and valid Google API credentials.

## 🔮 Future Enhancements

### **Phase 4: Real-Time Sync System** (Next)
- Background polling service for new reviews
- WebSocket connections for live updates
- Job queue system for async processing
- Sync orchestrator for multiple locations

### **Phase 5: Performance Optimization**
- Redis caching implementation
- Database query optimization
- API rate limiting and request deduplication
- Connection pooling

### **Phase 6: Advanced Features**
- AI-powered sentiment analysis
- Automated response templates
- Advanced reporting and analytics
- Multi-language support

### **Phase 7: Platform Expansion**
- TripAdvisor API integration
- Booking.com Partner API
- Additional review platforms
- Social media monitoring

### **Phase 8: Enterprise Features**
- Multi-hotel management
- Team collaboration tools
- Advanced user roles and permissions
- Custom branding and white-labeling

## 🤝 Contributing

This project is designed for hotel industry professionals looking to streamline their review management process. The modular architecture allows for easy extension and customization based on specific hotel needs.

## 📄 License

MIT License - See LICENSE file for details

## 🆘 Support

For technical support or feature requests, please refer to the project documentation or contact the development team.

---

**Hotel Review Manager** - Transforming hotel guest feedback management through intelligent automation and real-time insights.
