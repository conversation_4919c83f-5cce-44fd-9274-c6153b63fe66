import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { TopBar } from "@/components/TopBar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from "recharts";
import type { ReviewStats } from "@shared/schema";

export default function AnalyticsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [timeRange, setTimeRange] = useState("6months");

  const { data: stats, isLoading } = useQuery<ReviewStats>({
    queryKey: ["/api/statistics"],
    queryFn: api.getStatistics,
  });

  const ratingDistributionData = stats?.ratingDistribution 
    ? Object.entries(stats.ratingDistribution).map(([rating, count]) => ({
        rating: `${rating} Stars`,
        count,
        percentage: ((count / stats.totalReviews) * 100).toFixed(1),
      }))
    : [];

  const monthlyTrendData = stats?.monthlyTrend || [];

  const COLORS = ['hsl(var(--chart-1))', 'hsl(var(--chart-2))', 'hsl(var(--chart-3))', 'hsl(var(--chart-4))', 'hsl(var(--chart-5))'];

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />

      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          title="Analytics Dashboard"
          subtitle="Detailed insights into your review performance"
        />

        <main className="flex-1 overflow-auto p-6 custom-scrollbar">
          {/* Time Range Selector */}
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold text-foreground">Review Analytics</h3>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1month">Last Month</SelectItem>
                <SelectItem value="3months">Last 3 Months</SelectItem>
                <SelectItem value="6months">Last 6 Months</SelectItem>
                <SelectItem value="1year">Last Year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <div className="skeleton h-6 w-48" />
                  </CardHeader>
                  <CardContent>
                    <div className="skeleton h-64 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Rating Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Rating Distribution</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Breakdown of review ratings
                  </p>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={ratingDistributionData}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis 
                        dataKey="rating" 
                        className="text-xs fill-muted-foreground"
                      />
                      <YAxis className="text-xs fill-muted-foreground" />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'hsl(var(--card))',
                          border: '1px solid hsl(var(--border))',
                          borderRadius: '6px',
                        }}
                      />
                      <Bar 
                        dataKey="count" 
                        fill="hsl(var(--primary))"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Rating Percentage Pie Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Rating Percentage</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Proportion of each rating
                  </p>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={ratingDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ rating, percentage }) => `${rating}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {ratingDistributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Monthly Trend */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Review Trend Over Time</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Monthly review count and average rating
                  </p>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={monthlyTrendData}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis 
                        dataKey="month" 
                        className="text-xs fill-muted-foreground"
                      />
                      <YAxis 
                        yAxisId="left"
                        className="text-xs fill-muted-foreground"
                      />
                      <YAxis 
                        yAxisId="right" 
                        orientation="right"
                        domain={[0, 5]}
                        className="text-xs fill-muted-foreground"
                      />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'hsl(var(--card))',
                          border: '1px solid hsl(var(--border))',
                          borderRadius: '6px',
                        }}
                      />
                      <Bar 
                        yAxisId="left"
                        dataKey="count" 
                        fill="hsl(var(--chart-1))"
                        name="Review Count"
                        radius={[4, 4, 0, 0]}
                      />
                      <Line 
                        yAxisId="right"
                        type="monotone" 
                        dataKey="rating" 
                        stroke="hsl(var(--chart-2))" 
                        strokeWidth={3}
                        name="Average Rating"
                        dot={{ fill: 'hsl(var(--chart-2))', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Summary Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Total Reviews</span>
                    <span className="font-semibold">{stats?.totalReviews?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Average Rating</span>
                    <span className="font-semibold">{stats?.averageRating?.toFixed(1) || '0.0'}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Response Rate</span>
                    <span className="font-semibold">{stats?.responseRate || 0}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Pending Replies</span>
                    <span className={`font-semibold ${(stats?.pendingReplies || 0) > 0 ? 'text-amber-600' : 'text-green-600'}`}>
                      {stats?.pendingReplies || 0}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Insights */}
              <Card>
                <CardHeader>
                  <CardTitle>Key Insights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {stats && (
                    <>
                      {stats.averageRating >= 4.5 && (
                        <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-500">
                          <p className="text-sm text-green-800 dark:text-green-200">
                            ✨ Excellent rating performance! Keep up the great work.
                          </p>
                        </div>
                      )}
                      {stats.pendingReplies > 5 && (
                        <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border-l-4 border-amber-500">
                          <p className="text-sm text-amber-800 dark:text-amber-200">
                            ⚠️ You have {stats.pendingReplies} pending replies. Consider responding promptly.
                          </p>
                        </div>
                      )}
                      {stats.responseRate < 80 && (
                        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                          <p className="text-sm text-blue-800 dark:text-blue-200">
                            💡 Your response rate is {stats.responseRate}%. Aim for 90%+ to improve guest engagement.
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
